import React, { useState, useEffect, useMemo, useCallback, useRef } from "react";

import { Tab, Row, Col, Nav, Table, Form, Modal, Card } from 'react-bootstrap';

//import {_utilities} from '../SupplierScreen';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Dialog } from 'primereact/dialog';
import { Checkbox } from 'primereact/checkbox';
import { Button } from 'primereact/button';
import { Steps } from 'primereact/steps';
import Swal from "sweetalert2";
import moment from 'moment'
import { Toolbar } from 'primereact/toolbar';
import { InputTextarea } from 'primereact/inputtextarea'
import { RadioButton } from "primereact/radiobutton";
import useForceUpdate from "use-force-update";
import { Dropdown } from "primereact/dropdown";
import { FileUpload } from "primereact/fileupload";
import APIServices from "../../service/APIService";
import { API } from "../../constants/api_url";
import { DateTime } from "luxon";
import { useSelector } from "react-redux";
import { AccordionTab, Accordion } from "primereact/accordion";
import { Tag } from "primereact/tag";
import "../../Styles/_utilities.scss"
import { InputText } from 'primereact/inputtext';
import { AttachmentAsIcon } from "../../components/Forms/AttachmentAsIcon";
import * as XLSX from 'xlsx';
import FileSaver from 'file-saver';
import SupplierReport from "../../Dashboard/SupplierScreen/SupplierReport";
import { Calendar } from "primereact/calendar";


const categoryOptions = [
    { label: 'Good Practices', id: 1 },
    { label: 'Opportunity of Improvement', id: 2 },
    { label: 'Non-compliance', id: 3 },
];

const nonComplianceOptions = [
    { label: 'Regulatory (Major)', id: 1 },
    { label: 'Regulatory (Minor)', id: 2 },
    { label: 'Minor', id: 3 },
];



const AuditPanel = ({ auditId, closeModal, refresh, editable }) => {
    const login_data = useSelector((state) => state.user.userdetail);
    const admin_data = useSelector((state) => state.user.admindetail);
    const users = useSelector((state) => state.userlist.supplierList);
    const [reportdialog, setReportDialog] = useState(false)

    const [lawsOption, setLawOption] = useState([])
    const [data, setData] = useState({ data1: [], id: null })
    const [formId, setFormId] = useState(null)
    const [assessmentsection, setAssessmentSection] = useState([])
    const [assessmentsection1, setAssessmentSection1] = useState([])
    const [subsectiondata, setSubSectionData] = useState([])
    const [selectedsubsection2, setSelectedSubSection2] = useState(null)
    const [steps, setSteps] = useState([])
    const [activeIndex, setActiveIndex] = useState(0);
    const forceUpdate = useForceUpdate()
    const [headerValues, setHeaderValues] = useState({});
    const [files, setFiles] = useState([]);
    const [activeQuestionId, setActiveQuestionId] = useState(null);
    const [commentText, setCommentText] = useState('');
    const [selectedQuestion, setSelectedQuestion] = useState(null)
    const [selectSection, setSelectSection] = useState([])
    const [allCompleted, setAllCompleted] = useState(false)
    const [grandTotalScore, setGrandTotalScore] = useState(0);
    const [activeAccordionIndex, setActiveAccordionIndex] = useState(null);
    const categoryList = [{ name: 'Forging & Machining', value: 1 }, { name: 'Casting & Machining', value: 2 }, { name: 'Pressing & Fabrication', value: 3 }, { name: 'Proprietary Mechanical', value: 4 }, { name: 'Proprietary Electrical', value: 5 }, { name: 'Plastics, Rubber, Painting and Stickers', value: 6 }, { name: 'EV/3W/2W', value: 7 }, { name: 'BW', value: 8 }, { name: 'Accessories', value: 9 }, { name: 'IDM (Indirect Material)', value: 10 }, { name: 'Import', value: 11 }]

    const [supplierResponseData, setSupplierResponseData] = useState([])
    const [findings, setFindings] = useState([]);
    const [findingDialog, setFindingDialog] = useState(false);
    const [submitted, setSubmitted] = useState(false)
    const [deleteDialog, setDeleteDialog] = useState(false);
    const [selectSubSection1, setSelectSubSection1] = useState('')

    // This holds the current item being edited or created
    const [currentFinding, setCurrentFinding] = useState({

        categoryOfFinding: null,
        nonComplianceType: null,
        applicableLaw: null,
        otherLaw: '',
        finding: '',
        description: '',
        auditorAttachments: [],
    });

    useEffect(() => {
        console.log(editable, auditId)
        // Fetch findings when selectSubSection1 changes
        // const fetchFindings = async () => {
        //     if (!selectSubSection1) {
        //         setFindings([]); // Reset findings if selectSubSection1 is not provided
        //         return;
        //     }

        //     try {

        //         let uriString = {

        //             where: {

        //                 assessmentSubSection2Id: selectSubSection1.id
        //             }

        //         };
        //         const response = await APIServices.get(API.getFindingAllAuditor(auditId.id) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`);
        //         setFindings(response.data); // Populate findings state with the response data
        //     } catch (err) {


        //     }
        // };

        // fetchFindings();
    }, [selectSubSection1]);

    const [activeId, setActiveId] = useState(1); // State to hold the active framework ID
    const [labelColor, setLabelColor] = useState("#007bff"); // Default label color (black)
    const [result, setResult] = useState(null)
    const [supplierId, setSupplierId] = useState(null)
    const [highlightedQuestion, setHighlightedQuestion] = useState(null);

    // Auto save and timer state
    const [autoSaveTimer, setAutoSaveTimer] = useState(30);
    const [isAutoSaving, setIsAutoSaving] = useState(false);
    const [lastSaveTime, setLastSaveTime] = useState(null);
    const [isStepperSwitching, setIsStepperSwitching] = useState(false);
    const [isClosing, setIsClosing] = useState(false);
    const autoSaveIntervalRef = useRef(null);
    const timerIntervalRef = useRef(null);

    const handleFileChange = (file) => {

        setFiles(file)

    }
    const viewReport = () => {
        setReportDialog(true)
    }
    const handleComment = (question, questionIndex, sectionId, subsection1Id, subsection2Id) => {

        setSelectedQuestion({
            ...question,
            questionIndex,
            sectionId,
            subsection1Id,
            subsection2Id,
        });
        setComment(question.comment || '');
        setShowModal(true)
    }

    const openNew = () => {
        setSubmitted(false)
        setCurrentFinding({
            otherLaw: '',
            categoryOfFinding: null,
            nonComplianceType: null,
            applicableLaw: null,
            finding: '',
            description: '',
            auditorAttachments: [],
        });
        setFindingDialog(true);
    };

    const tagFromCategory = (cat) => cat === 'other' ? 'oc' : 'mc';
    const makeQuestionObject = (q) => ({
        /* --- UI-facing fields --- */
        id: q.id,
        label: q.text,
        type: translateType(q.type),
        placeholder: '',
        values: buildRadioValues(q),
        value: '',
        comment: '',
        attachments: [],
        mandatory: q.category === 'mandatory',
        isAttachmentMandatory: q.type === 'E' && q.category === 'mandatory',
        tag: tagFromCategory(q.category),
        /* --- keep everything else for later use --- */
        raw: { ...q },               // <— full backend blob

        /* (or expose individually if you prefer) */
        questionNumber: q.questionNumber,
        numerator: q.numerator,
        denominator: q.denominator,
        yesScore: q.yesScore,
        noScore: q.noScore,
        naScore: q.naScore,
        marksHint: q.marks,
        requiredResp: q.requiredResponse,
        category: q.category,
        order: q.order ?? 0,
        nc: {
            raised: false,               // toggled automatically
            mandatory: q.requiredResponse === 'NC' && q.category !== 'other',
            action: '',
            dueDate: null,
            evidence: [],
            supplierId: null,
            applicableLaw: null,   // dropdown id
            otherLaw: '',          // free-text when “Others”
            finding: '',           // short title
            description: '',       // long text

        },
        ofi: {
            raised: false,               // toggled automatically
            action: '',
            dueDate: null,
            evidence: [],
            supplierId: null,
            finding: '',           // short title
            description: '',
        },
    });
    const translateType = (src) => {
        switch (src) {
            case 'A':                       // Yes / No / NA
            case 'B':
            case 'C': return 'radio-group'; // ← C is now multi-choice
            case 'D': return 'textarea';
            case 'E': return 'attachment';
            default: return 'radio-group';
        }
    };
    const buildRadioValues = (q) => {
        /* A & B keep the Yes / No ( / N-A ) pattern */
        if (['A', 'B'].includes(q.type)) {
            return [
                { label: 'Yes', value: q.yesScore ?? 1, selected: false },
                { label: 'No', value: q.noScore ?? 0, selected: false },
                ...(q.naScore != null
                    ? [{ label: 'N/A', value: q.naScore, selected: false }]
                    : []),
            ];
        }

        /* NEW — type C: parse “>50% = 1, 100% = 2, …” */
        if (q.type === 'C' && typeof q.marks === 'string') {
            return q.marks
                .split(',')
                .map(s => s.trim())
                .filter(Boolean)
                .map((pair) => {
                    const [left, right] = pair.split('=');
                    return {
                        label: (left ?? '').trim(),              // e.g. “>50%”
                        value: Number((right ?? '').trim()) || 0, // e.g. 1
                        selected: false,
                    };
                });
        }

        /* everything else (D) has no predefined choices */
        return [];
    };
    function buildAssessmentSections(categories) {
        return categories.map((cat, catIdx) => ({
            id: cat.id,
            title: cat.name,
            order: catIdx,
            completed: false,
            hasProgress: false,
            sectionTotalScore: 0,

            /* supplySections → SubSection-1 */
            assessmentSubSection1s: cat.supplySections.map((sec, secIdx) => ({
                id: sec.id,
                title: sec.name,
                order: secIdx,
                totalScore: 0,
                totalCompleted: false,
                hasProgress: false,

                /* supplyChecklists → SubSection-2 */
                assessmentSubSection2s: sec.supplyChecklists.map((cl, clIdx) => {
                    const parsed = JSON.parse(cl.values);                  // checklist JSON
                    const questions = parsed.components?.[0]?.questions ?? [];
                    const qObjects = questions.map(makeQuestionObject);

                    return {
                        id: cl.id,
                        title: cl.name,
                        order: clIdx,
                        completed: false,
                        hasProgress: false,
                        form: {
                            data1: JSON.stringify(qObjects),   // ← what your editor consumes
                            score: 0,
                        },
                    };
                }),
            })),
        }));
    }
    // useEffect(() => {
    //     fetchCategories()
    // }, []);
    /* ────────── 1A. update a field inside NC  ────────── */
    // utils --------------------------------------------------------------
    const pickSubSection2 = (state, secId, s1Id, s2Id) => {
        const sec = state.find(s => s.id === secId);
        if (!sec) return null;
        const s1 = sec.assessmentSubSection1s.find(x => x.id === s1Id);
        if (!s1) return null;
        return s1.assessmentSubSection2s.find(x => x.id === s2Id) || null;
    };

    // 1A ─ updateNCField -------------------------------------------------
    const updateNCField = React.useCallback(
        (secId, s1Id, s2Id, qIdx, field, value) => {
            setAssessmentSection(prev => {

                /* clone-and-patch the big tree */
                const next = prev.map(sec => sec.id !== secId ? sec : {
                    ...sec,
                    assessmentSubSection1s: sec.assessmentSubSection1s.map(s1 =>
                        s1.id !== s1Id ? s1 : {
                            ...s1,
                            assessmentSubSection2s: s1.assessmentSubSection2s.map(s2 =>
                                s2.id !== s2Id ? s2 : (() => {
                                    const qs = JSON.parse(s2.form.data1);
                                    qs[qIdx].nc = { ...qs[qIdx].nc, [field]: value };
                                    return { ...s2, form: { ...s2.form, data1: JSON.stringify(qs) } };
                                })()
                            ),
                        }
                    ),
                });

                /* ✨ keep the right pane in sync */
                const fresh = pickSubSection2(next, secId, s1Id, s2Id);
                if (fresh) setSelectSection(fresh);

                return next;
            });
        }, []
    );

    // 1B ─ updateOFIField (identical idea) -------------------------------


    /* ────────── 1B. update a field inside OFI / CP  ────────── */
    // helper – find the freshly-edited sub-sub-section -------------
    const pickSubSection21 = (tree, secId, s1Id, s2Id) => {
        const sec = tree.find(s => s.id === secId);
        if (!sec) return null;
        const s1 = sec.assessmentSubSection1s.find(x => x.id === s1Id);
        if (!s1) return null;
        return s1.assessmentSubSection2s.find(x => x.id === s2Id) || null;
    };

    // ───────────────────────────────────────────────────────────────
    const updateOFIField = React.useCallback(
        (secId, s1Id, s2Id, qIdx, field, value) => {
            setAssessmentSection(prev => {
                /* ---------- clone & patch the full tree ---------- */
                const next = prev.map(sec => sec.id !== secId ? sec : {
                    ...sec,
                    assessmentSubSection1s: sec.assessmentSubSection1s.map(s1 =>
                        s1.id !== s1Id ? s1 : {
                            ...s1,
                            assessmentSubSection2s: s1.assessmentSubSection2s.map(s2 =>
                                s2.id !== s2Id ? s2 : (() => {
                                    const qs = JSON.parse(s2.form.data1);
                                    qs[qIdx].ofi = { ...qs[qIdx].ofi, [field]: value };
                                    return { ...s2, form: { ...s2.form, data1: JSON.stringify(qs) } };
                                })()
                            ),
                        }
                    ),
                });

                /* ---------- keep right-hand pane up-to-date ---------- */
                const fresh = pickSubSection21(next, secId, s1Id, s2Id);
                if (fresh) setSelectSection(fresh);

                return next;
            });
        },
        []
    );


    const fetchCategories = async () => {
        const uriString = {
            include: [
                {
                    relation: "supplySections",
                    scope: {
                        include: [{ relation: "supplyChecklists" }]
                    }
                }
            ]
        };

        try {
            const response = await APIServices.get(
                `${API.SupplyCategories}?filter=${encodeURIComponent(JSON.stringify(uriString))}`
            );
            const built = buildAssessmentSections(response.data || []);
            setAssessmentSection(built);
            setAssessmentSection1(built)       // ‼️ this drives the stepper/accordion
            setSteps(built.map(s => ({ label: s.title, id: s.id })));
            // setCategories(response.data || []);
            // console.log(built);

            // Trigger auto-save setup now that we have assessment data
            console.log("Assessment data loaded, auto-save should be available now");
        } catch (error) {
            console.error("Error fetching categories:", error);
        }
    };

    // Handle editing an existing finding
    const editFinding = (rowData) => {
        setCurrentFinding({ ...rowData }); // clone the existing data
        setFindingDialog(true);
    };

    // Hide the dialog
    const hideDialog = () => {
        setFindingDialog(false);
        // Trigger auto save when dialog is closed (only if editable)
        if (editable) {
            console.log("Dialog closed - triggering auto save");
            performAutoSaveRef.current();
        }
    };

    // Handle saving (create or update)
    const saveFinding = async () => {
        setSubmitted(true)
        console.log(currentFinding)
        if (currentFinding.categoryOfFinding && (currentFinding.categoryOfFinding === 3 ? ((currentFinding.applicableLaw != null && (currentFinding.applicableLaw === 0 ? currentFinding?.otherLaw?.trim().length : true)) && currentFinding.nonComplianceType) : true) && currentFinding.finding.trim().length) {
            if (currentFinding.id) {

                let newObj = { ...currentFinding }
                delete newObj.priority
                delete newObj.supplierAttachments
                delete newObj.created_on
                delete newObj.created_by
                delete newObj.vendorCodeId

                // UPDATE
                APIServices.patch(`${API.SupplierAction_Edit(currentFinding.id)}`, { ...newObj, auditor_modified_by: login_data.id, auditor_modified_on: DateTime.utc() }).then((res) => {
                    let loc = JSON.parse(JSON.stringify(findings))
                    let index = loc.findIndex(x => x.id === currentFinding.id)
                    if (index !== -1) {
                        loc[index] = { ...loc[index], ...newObj }
                        setFindings(loc)
                    }
                })

                // Update the state with the updated finding
                // setFindings((prev) =>
                //     prev.map((f) => (f.id === currentFinding.id ? response.data : f))
                // );

                setSelectSubSection1(selectSubSection1)
            } else {
                // CREATE

                const response = await APIServices.post(API.addFinding(auditId.id), { assessmentSubSection2Id: selectSubSection1.id, created_on: DateTime.utc(), created_by: login_data.id, ...currentFinding });
                setFindings((prev) => [...prev, response.data]);
            }
            setFindingDialog(false);
        }
        // setCurrentFinding({
        //     id: null,
        //     categoryOfFinding: null,
        //     nonComplianceType: null,
        //     applicableLaw: null,
        //     finding: '',
        //     description: '',
        //     auditorAttachments: [],
        // });

    };

    // Handle opening the delete confirmation
    const confirmDeleteFinding = async (rowData) => {
        const { value: accept } = await Swal.fire({
            title: `<div style="overflow:visible;font-size:20px;font-weight:600;margin-top:0px">Warning</div>`,
            html: `<div style="overflow:auto;max-height:200px" >Are you sure want to delete "${rowData.finding}"</div>`,

            confirmButtonColor: 'red',
            showCancelButton: true,

            confirmButtonText:
                'Delete',

        })
        if (accept) {
            APIServices.delete(API.SupplierAction_Edit(rowData.id)).then((res) => {
                let loc = JSON.parse(JSON.stringify(findings))
                let index = loc.findIndex(x => x.id === rowData.id)
                if (index !== -1) {
                    loc.splice(index, 1);
                    setFindings(loc)
                }


                forceUpdate()
            })
        }

    };

    // Hide delete confirmation
    const hideDeleteDialog = () => {
        setDeleteDialog(false);
        // Trigger auto save when delete dialog is closed (only if editable)
        if (editable) {
            console.log("Delete dialog closed - triggering auto save");
            performAutoSaveRef.current();
        }
    };

    // Perform delete
    const deleteFinding = () => {
        setFindings((prev) => prev.filter((f) => f.id !== currentFinding.id));
        setDeleteDialog(false);
        setCurrentFinding({});
    };

    // Action column (Edit/Delete buttons)
    const actionBodyTemplate = (rowData) => {
        return (
            <React.Fragment>
                <Button
                    icon="pi pi-pencil"
                    className="p-button-rounded p-button-success mr-2"
                    onClick={() => editFinding(rowData)}
                />
                <Button
                    icon="pi pi-trash"
                    className="p-button-rounded p-button-warning"
                    onClick={() => confirmDeleteFinding(rowData)}
                />
            </React.Fragment>
        );
    };

    // *** DIALOG FOOTERS ***

    const findingDialogFooter = (
        <React.Fragment>
            <Button
                label="Cancel"
                icon="pi pi-times"
                className="p-button-text"
                onClick={hideDialog}
            />
            <Button label="Save" icon="pi pi-check" className="p-button-text" onClick={saveFinding} />
        </React.Fragment>
    );

    const deleteDialogFooter = (
        <React.Fragment>
            <Button
                label="No"
                icon="pi pi-times"
                className="p-button-text"
                onClick={hideDeleteDialog}
            />
            <Button
                label="Yes"
                icon="pi pi-check"
                className="p-button-text"
                onClick={deleteFinding}
            />
        </React.Fragment>
    );

    const uploadFilesApi = async (file, item, event) => {

        setSelectedQuestion(item)
        let promise = new Promise((resolve, rej) => {
            if (file.size <= 10000000) {
                let formData = new FormData();
                formData.append("file", file);
                APIServices.post(API.FilesUpload, formData, {
                    headers: {
                        "content-type": "multipart/form-data",
                    },
                    mode: "no-cors",
                }).then((res) => {

                    if (res.status === 200) {
                        resolve({
                            url: API.Docs + res.data.files[0].originalname,
                            name: res.data.files[0].originalname,
                            size: res.data.files[0].size,
                        });
                    } else {
                        resolve(null);
                    }
                });
            } else {
                resolve(null);
            }
        });

        return promise;
    };
    const checkQuestionary = useCallback((data) => {
        try {
            let loc = JSON.parse(data)
            if (loc.length) {
                return loc.every((item) => { return (item.type === 'select' ? item.value : item.type === 'radio-group' ? item?.values?.some((v) => v?.selected) : item.type === 'textarea' ? item.value && item.value?.trim()?.length : true) })
            } else {
                return false
            }
        } catch (error) {
            console.error('Error in checkQuestionary:', error);
            return false;
        }
    }, []);
    const checkSubSection1 = useCallback((data) => {
        try {
            if (data?.assessmentSubSection2s?.length) {
                return data?.assessmentSubSection2s.every((item) => { return checkQuestionary(item?.form?.data1 || '[]') })
            } else {
                return false
            }
        } catch (error) {
            console.error('Error in checkSubSection1:', error);
            return false;
        }
    }, [checkQuestionary]);
    const handleSaveComment = () => {
        if (!selectedQuestion) return; // If somehow nothing selected

        const { sectionId, subsection1Id, subsection2Id, questionIndex } = selectedQuestion;

        const updated = assessmentsection.map((sectionItem) => {
            if (sectionItem.id !== sectionId) return sectionItem;
            return {
                ...sectionItem,
                assessmentSubSection1s: sectionItem.assessmentSubSection1s.map((s1Item) => {
                    if (s1Item.id !== subsection1Id) return s1Item;
                    return {
                        ...s1Item,
                        assessmentSubSection2s: s1Item.assessmentSubSection2s.map((s2Item) => {
                            if (s2Item.id !== subsection2Id) return s2Item;

                            // Parse data1
                            const parsedData = JSON.parse(s2Item.form.data1);

                            // Grab the specific question from parsedData
                            const questionToUpdate = { ...parsedData[questionIndex] };
                            // Update its comment
                            questionToUpdate.comment = comment;

                            // Put it back
                            parsedData[questionIndex] = questionToUpdate;

                            // Return the updated sub-subsection
                            return {
                                ...s2Item,
                                form: {
                                    ...s2Item.form,
                                    data1: JSON.stringify(parsedData), // Overwrite data1 with updated questions
                                },
                            };
                        }),
                    };
                }),
            };
        });

        // Set the new array in state
        setAssessmentSection(updated);

        // Optionally, if you want to keep local `selectSection` in sync, re-find the updated subsection:
        const newSection = updated.find((s) => s.id === sectionId);
        const newSubSection1 = newSection.assessmentSubSection1s.find((x) => x.id === subsection1Id);
        const newSubSection2 = newSubSection1.assessmentSubSection2s.find((x) => x.id === subsection2Id);
        setSelectSection(newSubSection2);

        // Close the dialog
        setShowModal(false);
    };

    const handleAttachment = (file, ques) => {
        let loc = JSON.parse(JSON.stringify(data))
        let index = loc.data1.findIndex(i => i.name === ques?.name)
        if (index !== -1) {
            loc.data1[index].attachments = [file]
            setData(loc)
            forceUpdate()
        }

    };
    const [showModal, setShowModal] = useState(false);
    const [comment, setComment] = useState('');

    const handleOpenModal = () => { setShowModal(true) };
    const handleCloseModal = () => {
        setShowModal(false);
        // Trigger auto save when comment modal is closed (only if editable)
        if (editable) {
            console.log("Comment modal closed - triggering auto save");
            performAutoSaveRef.current();
        }
    };

    const handleCloseWithAutoSave = async () => {
        console.log("Close button clicked - triggering auto save before closing");
        // Trigger auto save before closing the dialog (only if editable)
        if (editable) {
            setIsClosing(true);
            try {
                // Wait for 30 seconds (30000 milliseconds) before auto-saving
                await new Promise(resolve => setTimeout(resolve, 20000));

                // Then perform auto save
                await performAutoSaveRef.current();

                // Close the modal after a short delay to allow auto save to complete
                setTimeout(() => {
                    setIsClosing(false);
                    closeModal(false);
                }, 500);
            } catch (error) {
                console.error("Error during auto save:", error);
                setIsClosing(false);
                closeModal(false);
            }
        } else {
            // Close immediately if not editable
            closeModal(false);
        }
    };



    // useEffect(() => {
    //     const promise0 = APIServices.post(API.sectionAuditData_UP, { assignmentId: auditId?.id })
    //     const promise1 = APIServices.get(API.GetList_DD(2))
    //     Promise.all([promise0, promise1]).then((values) => {
    //         setLawOption([...values[1].data, { id: 0, title: 'Others' }])

    //         setResult(values[0]?.data?.result)
    //         console.log(values[0]?.data?.result)
    //         if (values[0]?.data?.result === 2) {
    //             setSupplierId(values[0]?.data?.id)
    //             setGrandTotalScore(values[0]?.data?.auditorMSIScore || 0)
    //         }

    //         if (values[0]?.data?.response) {
    //             //  setSteps(values[0]?.data.sort((a, b) => { return a.order - b.order }, 0).map(i => ({ label: i.title, id: i.id })))
    //             setAssessmentSection(
    //                 values[0]?.data?.result === 2
    //                     ? JSON.parse(values[0]?.data?.response).sort((a, b) => a.order - b.order)
    //                     : assessmentsection1
    //             );
    //             setSupplierResponseData(values[0]?.data?.supplierResponse?.length !== 0 ? (JSON.parse(values[0]?.data?.supplierResponse).sort((a, b) => { return a.order - b.order }, 0)) : [])
    //             setSubSectionData(JSON.parse(values[0]?.data?.response).sort((a, b) => { return a.order - b.order }, 0)[0])
    //         } else {
    //             setSteps([])
    //             setAssessmentSection([])
    //         }

    //     })
    // }, [auditId])


    useEffect(() => {
        const promise0 = APIServices.post(API.sectionAuditData_UP, { assignmentId: auditId?.id });
        const promise1 = APIServices.get(API.GetList_DD(2));

        Promise.all([promise0, promise1]).then(([secRes, lawRes]) => {
            setLawOption([...lawRes.data, { id: 0, title: "Others" }]);

            setResult(secRes.data?.result ?? null);
            if (secRes.data?.result === 2) {
                setSupplierId(secRes.data.id);
                // setGrandTotalScore(secRes.data.auditorMSIScore || 0);

                setGrandTotalScore(
                    (secRes.data.auditorMSIScore || 0)
                );
            }

            /* ---------- update the big tree safely ---------- */
            setAssessmentSection(prev =>
                secRes.data?.result === 2 && secRes.data?.response
                    ? JSON.parse(secRes.data.response).sort((a, b) => a.order - b.order)
                    : prev
            );

            /* ---------- other state that depends on response ---------- */
            if (secRes.data?.supplierResponse) {
                setSupplierResponseData(secRes.data.supplierResponse.length !== 0 ?
                    JSON.parse(secRes.data.supplierResponse).sort((a, b) => a.order - b.order) : []
                );
            }

            if (secRes.data?.response) {
                const parsed = JSON.parse(secRes.data.response).sort((a, b) => a.order - b.order);
                setSubSectionData(parsed[0]);
            }
            if (secRes.data?.response === "[]" || secRes.data?.result === 0 || secRes.data?.result === 1) {
                // Call fetchCategories after all data is processed
                fetchCategories();
            }
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [auditId]);



    const handleStepClick = async (sec, index) => {
        console.log("Stepper clicked: Saving previous section and loading data for section", sec.title);

        // Set loading state for stepper switching
        setIsStepperSwitching(true);

        // Update subsectiondata first to ensure auto-save has the correct section data
        const newSubSectionData = assessmentsection.find(i => i.id === sec.id);
        setSubSectionData(newSubSectionData);

        try {
            // Only perform auto-save and auto-retrieve if editable is true
            if (editable) {
                // First, save the current section data before switching
                console.log("Saving previous stepper section before switching");

                // Create a modified version of performAutoSavePromise that uses the current section data
                const performStepperAutoSave = () => {
                    return new Promise((resolve, reject) => {
                        // Only perform auto save if editable is true
                        if (!editable) {
                            console.log("Auto save skipped - not editable");
                            resolve();
                            return;
                        }

                        // Use current subsectiondata or the new section data
                        const currentSubsectionData = subsectiondata?.id ? subsectiondata : newSubSectionData;

                        if (!auditId?.vendor?.code || !auditId?.vendor?.id || !currentSubsectionData?.id) {
                            console.log("Auto save skipped - missing required data:", {
                                vendorCode: auditId?.vendor?.code,
                                vendorId: auditId?.vendor?.id,
                                subsectionId: currentSubsectionData?.id
                            });
                            resolve();
                            return;
                        }

                        setIsAutoSaving(true);
                        console.log("Auto save triggered at", new Date().toLocaleTimeString());

                        if ((result === 1 || result === 0)) {
                            const requestBody = {
                                type: 0,
                                response: JSON.stringify(assessmentsection),
                                created_on: DateTime.utc(),
                                modified_on: DateTime.utc(),
                                created_by: login_data.id,
                                userProfileId: admin_data.id,
                                auditorMSIScore: grandTotalScore,
                                status: 0,
                                submitted_on: null,
                                supplierAssessmentAssignmentId: auditId.id,
                                submitted_by: null,
                                vendorCode: auditId?.vendor?.code,
                                vendorId: auditId?.vendor?.id
                            };

                            APIServices.post(API.AuditorChecklistSubmission_Custom(currentSubsectionData.id), requestBody)
                                .then(response => {
                                    if (response?.data?.result) {
                                        setSupplierId(response?.data?.id || null);
                                        setResult(response?.data?.id ? 2 : 0);
                                        setLastSaveTime(new Date());
                                        console.log("Auto save successful");
                                    }
                                    setIsAutoSaving(false);
                                    resolve(response);
                                })
                                .catch(error => {
                                    console.error("Auto save failed:", error);
                                    setIsAutoSaving(false);
                                    reject(error);
                                });
                        } else if (result === 2) {
                            const requestBody = {
                                submitted_on: null,
                                submitted_by: null,
                                response: JSON.stringify(assessmentsection),
                                modified_on: DateTime.utc(),
                                supplierAssessmentAssignmentId: auditId.id,
                                modified_by: login_data.id,
                                auditorMSIScore: grandTotalScore,
                                type: 0
                            };

                            APIServices.post(API.AuditorChecklistSubmission_Custom(currentSubsectionData.id), requestBody)
                                .then(response => {
                                    if (response?.data?.result) {
                                        setSupplierId(response?.data?.id || null);
                                        setResult(response?.data?.id ? 2 : 0);
                                        setLastSaveTime(new Date());
                                        console.log("Auto save successful");
                                    }
                                    setIsAutoSaving(false);
                                    resolve(response);
                                })
                                .catch(error => {
                                    console.error("Auto save failed:", error);
                                    setIsAutoSaving(false);
                                    reject(error);
                                });
                        } else {
                            setIsAutoSaving(false);
                            resolve();
                        }
                    });
                };

                // Wait for auto-save to complete before proceeding
                await performStepperAutoSave();
                console.log("Auto save completed, now performing auto retrieve");

                // Auto retrieve functionality - fetch the latest saved data for this section
                const res = await APIServices.post(API.AuditorChecklistSubmission_Recent, {
                    supplierAssessmentAssignmentId: auditId.id,
                    sectionId: sec.id
                });

                if (res?.data?.result === 2) {
                    setSupplierId(res?.data?.id || null);
                    const retrievedData = JSON.parse(res?.data?.response || '[]');
                    setAssessmentSection(retrievedData);
                    setResult(2);
                    console.log("Auto retrieve successful: Data loaded for section", sec.title);

                    // Update last save time if data was retrieved
                    if (res?.data?.modified_on) {
                        setLastSaveTime(new Date(res.data.modified_on));
                    }
                } else {
                    console.log("Auto retrieve: No saved data found for section", sec.title);
                }
            } else {
                console.log("Auto save and auto retrieve skipped - not editable, just switching section");
            }
        } catch (error) {
            console.error("Auto save or auto retrieve failed:", error);
        } finally {
            // Always clear the loading state
            setIsStepperSwitching(false);
        }

        // Reset UI state for new section
        setActiveAccordionIndex(null);
        setSelectedSubSection2(null);
        setSelectSection(null);
        setActiveIndex(index);
    };

    const [expandedItems, setExpandedItems] = useState([]); // Track expanded items
    const [expandAll, setExpandAll] = useState(false); // State to toggle expand all items




    const calculateSum = (data1) => {
        let sum = 0;

        try {
            const parsedData = JSON.parse(data1); // Parse the data1 JSON string
            parsedData.forEach((item) => {
                if (item.type === "radio-group") {
                    item.values.forEach((value) => {
                        if (value.selected) {
                            const numericValue = parseFloat(value.value);
                            if (!isNaN(numericValue)) {
                                sum += numericValue;
                            }
                        }
                    });
                }
            });
        } catch (error) {
            console.error("Error parsing data1:", error);
        }

        return sum;
    };

    /**
 * Checks completion status of questions in parsedData.
 * Returns an object with completion status information.
 */
    const checkQuestionsStatus = (parsedData) => {
        if (!parsedData || parsedData.length === 0) {
            return { allAnswered: false, hasProgress: false, totalQuestions: 0, answeredQuestions: 0 };
        }

        let answeredCount = 0;
        const totalCount = parsedData.length;

        parsedData.forEach((question) => {
            if (question.type === "radio-group" && question.values) {
                // Check if at least one value is selected
                if (question.values.some((v) => v.selected)) {
                    answeredCount++;
                }
            } else if (question.type === "textarea") {
                // Check if textarea has content
                if (question.value && question.value.trim().length > 0) {
                    answeredCount++;
                }
            } else {
                // For other question types, assume answered if they have a value
                if (question.value) {
                    answeredCount++;
                }
            }
        });

        return {
            allAnswered: answeredCount === totalCount,
            hasProgress: answeredCount > 0,
            totalQuestions: totalCount,
            answeredQuestions: answeredCount
        };
    };

    // Keep the old function for backward compatibility
    const checkAllQuestionsAnswered = (parsedData) => {
        const status = checkQuestionsStatus(parsedData);
        return status.allAnswered;
    }


    const onRadioButtonSelected = (
        question,
        cbind,
        auditId,
        section,
        subsection1,
        selectedsubsection2,
        formId,
        index
    ) => {
        const updatedAssessmentSection = assessmentsection.map((sectionItem) => {
            if (sectionItem.id !== section) return sectionItem;

            const updatedSubSection1s = sectionItem.assessmentSubSection1s.map(
                (subsection1Item) => {
                    if (subsection1Item.id !== subsection1) return subsection1Item;

                    // Update *all* subSection2
                    const updatedSubSection2s = subsection1Item.assessmentSubSection2s.map(
                        (subsection2Item) => {
                            if (subsection2Item.id !== selectedsubsection2) return subsection2Item;

                            const parsedData = JSON.parse(subsection2Item.form.data1);
                            const updatedQuestion = { ...parsedData[index] };

                            if (updatedQuestion && updatedQuestion.values) {
                                updatedQuestion.values = updatedQuestion.values.map((value, idx) => ({
                                    ...value,
                                    selected: idx === cbind,
                                }));

                                /* ─── decide if NC / OFI banner must appear ─── */
                                const picked = updatedQuestion.values[cbind];
                                const reqResp = String(updatedQuestion.requiredResp || '').trim().toUpperCase();

                                /* NC: only when  requiredResp === 'NC'  AND  score-0  AND  not N/A */
                                // updatedQuestion.nc.raised =
                                //     reqResp === 'NC' &&
                                //     Number(picked.value) === 0 &&
                                //     picked.label?.toUpperCase() !== 'N/A';

                                // /* OFI / CP: optional-category questions that score 0 */
                                // updatedQuestion.ofi.raised =
                                //     updatedQuestion.category === 'other' &&
                                //     Number(picked.value) === 0 &&
                                //     picked.label?.toUpperCase() !== 'N/A';


                                const isZeroScore = Number(picked.value) === 0 &&
                                    picked.label?.toUpperCase() !== 'N/A';
                                const isOC = updatedQuestion.category === 'other';   // optional-category
                                const isMC = !isOC;

                                const hasYesAndNoWithZero = updatedQuestion.values.some(v => v.label?.toUpperCase() === 'YES' && Number(v.value) === 0) &&
                                    updatedQuestion.values.some(v => v.label?.toUpperCase() === 'NO' && Number(v.value) === 0);

                                // Reset both flags first
                                updatedQuestion.nc.raised = false;
                                updatedQuestion.ofi.raised = false;

                                // Apply NC/OFI rules only if not exempted by Yes/No rule
                                if (!hasYesAndNoWithZero || picked.label?.toUpperCase() === 'N/A') {
                                    // ① NC rules
                                    if (reqResp === 'NC' && isZeroScore) {
                                        updatedQuestion.nc.raised = true;
                                        updatedQuestion.nc.mandatory = isMC; // mc => mandatory, oc => not mandatory
                                    }

                                    // ② OFI / CP rules
                                    if (isOC && (reqResp === 'OFI' || reqResp === 'CP') && isZeroScore) {
                                        updatedQuestion.ofi.raised = true;
                                    }
                                }// mandatory-category

                                // /* reset both flags first */
                                // updatedQuestion.nc.raised = false;
                                // updatedQuestion.ofi.raised = false;

                                // /* ① NC rules ---------------------------------------------------- */
                                // if (reqResp === 'NC' && isZeroScore) {
                                //     updatedQuestion.nc.raised = true;
                                //     updatedQuestion.nc.mandatory = isMC; // mc => mandatory, oc => not mandatory
                                // }

                                // /* ② OFI / CP rules --------------------------------------------- */
                                // if (isOC && (reqResp === 'OFI' || reqResp === 'CP') && isZeroScore) {
                                //     updatedQuestion.ofi.raised = true;
                                // }
                                updatedQuestion.modified_by = login_data.id;
                                updatedQuestion.modified_on = DateTime.utc();
                            }

                            parsedData[index] = updatedQuestion;
                            const newScore = calculateSum(JSON.stringify(parsedData));
                            const questionStatus = checkQuestionsStatus(parsedData);

                            return {
                                ...subsection2Item,
                                completed: questionStatus.allAnswered,
                                hasProgress: questionStatus.hasProgress,
                                form: {
                                    ...subsection2Item.form,
                                    data1: JSON.stringify(parsedData),
                                    score: newScore,
                                },
                            };
                        }
                    );

                    // Summation for subSection1
                    const totalScoreForSubsection1 = updatedSubSection2s.reduce((acc, sub2) => {
                        return acc + (sub2.form?.score || 0);
                    }, 0);

                    const allSub2Completed =
                        updatedSubSection2s.length > 0 &&
                        updatedSubSection2s.every((sub2) => sub2.completed);

                    const hasSub2Progress =
                        updatedSubSection2s.length > 0 &&
                        updatedSubSection2s.some((sub2) => sub2.hasProgress);

                    return {
                        ...subsection1Item,
                        assessmentSubSection2s: updatedSubSection2s,
                        totalScore: totalScoreForSubsection1,
                        totalCompleted: allSub2Completed,
                        hasProgress: hasSub2Progress,
                    };
                }
            );

            // Now check if entire Section is completed
            const allSub1Completed =
                updatedSubSection1s.length > 0 &&
                updatedSubSection1s.every((sub1) => sub1.totalCompleted);

            // Check if section has any progress
            const sectionHasProgress =
                updatedSubSection1s.length > 0 &&
                updatedSubSection1s.some((sub1) => sub1.hasProgress);

            // 5) Compute total score for the entire Section
            const totalScoreForSection = updatedSubSection1s.reduce((acc, sub1) => {
                return acc + (sub1.totalScore || 0);
            }, 0);

            return {
                ...sectionItem,
                assessmentSubSection1s: updatedSubSection1s,
                sectionTotalScore: totalScoreForSection,
                completed: allSub1Completed, // entire section
                hasProgress: sectionHasProgress, // section has some progress
            };
        });

        setAssessmentSection(updatedAssessmentSection);

        // const overallScore = updatedAssessmentSection.reduce((acc, sec) => {
        //     return acc + (sec.sectionTotalScore || 0);
        // }, 0);

        // // 2) Save it in your separate state
        // setGrandTotalScore(overallScore);

        const overallScore = updatedAssessmentSection.reduce(
            (acc, sec) => acc + (sec.sectionTotalScore || 0),
            0
        );

        // ➜ divide by 3 as requested
        setGrandTotalScore(overallScore / 3);

        const isEverythingCompleted =
            updatedAssessmentSection.length > 0 &&
            updatedAssessmentSection.every((sec) => sec.completed === true);

        // 2) Store that in local state
        setAllCompleted(isEverythingCompleted);

        // -- Re-find the updated SubSection2 for `selectSection` --
        const newSectionItem = updatedAssessmentSection.find((s) => s.id === section);
        const newSubSection1Item = newSectionItem.assessmentSubSection1s.find(
            (s1) => s1.id === subsection1
        );
        const newSubSection2Item = newSubSection1Item.assessmentSubSection2s.find(
            (s2) => s2.id === selectedsubsection2
        );

        // Update local state reference (if you're using `selectSection` in rendering):
        setSelectSection(newSubSection2Item);

        // Optionally force a re-render:
        forceUpdate();


    };



    const getDate = (date, format) => {
        if (!date) {
            return 'Not Set'
        }
        if (typeof date === 'string') {
            return DateTime.fromISO(date, { zone: 'utc' }).toLocal().toFormat(format ? format : 'dd-MM-yyyy')
        } else if (DateTime.isDateTime(date)) {
            return date.toFormat(format ? format : 'dd-MM-yyyy')
        } else {
            return DateTime.fromJSDate(date).toLocal().toFormat(format ? format : 'dd-MM-yyyy')
        }

    };
    const onChangeDropwdown = (newValue,
        questionIndex,
        sectionId,
        subsection1Id,
        subsection2Id) => {
        const updatedAssessmentSection = assessmentsection.map((sec) => {
            // Only update the matching section
            if (sec.id !== sectionId) return sec;

            return {
                ...sec,
                assessmentSubSection1s: sec.assessmentSubSection1s.map((sub1) => {
                    if (sub1.id !== subsection1Id) return sub1;

                    // 2) Update SubSection2 array
                    const updatedSub2s = sub1.assessmentSubSection2s.map((sub2) => {
                        if (sub2.id !== subsection2Id) return sub2;

                        // 3) Parse the questions in data1
                        const parsedData = JSON.parse(sub2.form.data1);
                        const updatedQuestion = { ...parsedData[questionIndex] };

                        // 4) Set the new text value + metadata
                        updatedQuestion.value = newValue;
                        updatedQuestion.modified_by = login_data.id;
                        updatedQuestion.modified_on = DateTime.utc().toFormat('dd-MM-yyyy');

                        // 5) Put the updated question back
                        parsedData[questionIndex] = updatedQuestion;

                        return {
                            ...sub2,
                            form: {
                                ...sub2.form,
                                data1: JSON.stringify(parsedData), // Overwrite data1 with updated questions
                            },
                        };
                    });

                    return {
                        ...sub1,
                        assessmentSubSection2s: updatedSub2s,
                    };
                }),
            };
        });

        // 6) Update your state
        setAssessmentSection(updatedAssessmentSection);

        // (Optional) Re-find the updated sub2 item if you’re using selectSection in your UI
        const newSection = updatedAssessmentSection.find((s) => s.id === sectionId);
        const newSubSection1 = newSection.assessmentSubSection1s.find(
            (s1) => s1.id === subsection1Id
        );
        const newSubSection2 = newSubSection1.assessmentSubSection2s.find(
            (s2) => s2.id === subsection2Id
        );
        setSelectSection(newSubSection2);

        // Force a re-render if needed
        forceUpdate();
    }

    const onTextareaChange = (
        newValue,
        questionIndex,
        sectionId,
        subsection1Id,
        subsection2Id
    ) => {
        // 1) Build an updated copy of assessmentsection
        const updatedAssessmentSection = assessmentsection.map((sec) => {
            // Only update the matching section
            if (sec.id !== sectionId) return sec;

            const updatedSubSection1s = sec.assessmentSubSection1s.map((sub1) => {
                if (sub1.id !== subsection1Id) return sub1;

                // 2) Update SubSection2 array
                const updatedSub2s = sub1.assessmentSubSection2s.map((sub2) => {
                    if (sub2.id !== subsection2Id) return sub2;

                    // 3) Parse the questions in data1
                    const parsedData = JSON.parse(sub2.form.data1);
                    const updatedQuestion = { ...parsedData[questionIndex] };

                    // 4) Set the new text value + metadata
                    updatedQuestion.value = newValue;
                    updatedQuestion.modified_by = login_data.id;
                    updatedQuestion.modified_on = DateTime.utc().toFormat('dd-MM-yyyy');

                    // 5) Put the updated question back
                    parsedData[questionIndex] = updatedQuestion;

                    // 6) Check completion status
                    const questionStatus = checkQuestionsStatus(parsedData);

                    return {
                        ...sub2,
                        completed: questionStatus.allAnswered,
                        hasProgress: questionStatus.hasProgress,
                        form: {
                            ...sub2.form,
                            data1: JSON.stringify(parsedData), // Overwrite data1 with updated questions
                        },
                    };
                });

                // Calculate subsection1 completion status
                const allSub2Completed =
                    updatedSub2s.length > 0 &&
                    updatedSub2s.every((sub2) => sub2.completed);

                const hasSub2Progress =
                    updatedSub2s.length > 0 &&
                    updatedSub2s.some((sub2) => sub2.hasProgress);

                return {
                    ...sub1,
                    assessmentSubSection2s: updatedSub2s,
                    totalCompleted: allSub2Completed,
                    hasProgress: hasSub2Progress,
                };
            });

            // Calculate section completion status
            const allSub1Completed =
                updatedSubSection1s.length > 0 &&
                updatedSubSection1s.every((sub1) => sub1.totalCompleted);

            const sectionHasProgress =
                updatedSubSection1s.length > 0 &&
                updatedSubSection1s.some((sub1) => sub1.hasProgress);

            return {
                ...sec,
                assessmentSubSection1s: updatedSubSection1s,
                completed: allSub1Completed,
                hasProgress: sectionHasProgress,
            };
        });

        // 6) Update your state
        setAssessmentSection(updatedAssessmentSection);

        // (Optional) Re-find the updated sub2 item if you’re using selectSection in your UI
        const newSection = updatedAssessmentSection.find((s) => s.id === sectionId);
        const newSubSection1 = newSection.assessmentSubSection1s.find(
            (s1) => s1.id === subsection1Id
        );
        const newSubSection2 = newSubSection1.assessmentSubSection2s.find(
            (s2) => s2.id === subsection2Id
        );
        setSelectSection(newSubSection2);
    };
    const onFileUpload = async (
        uploadedFile,
        questionIndex,
        sectionId,
        subsection1Id,
        subsection2Id
    ) => {
        console.log(uploadedFile, questionIndex,
            sectionId,
            subsection1Id,
            subsection2Id)
        try {
            console.log(uploadedFile)
            // Ensure assessmentsection exists
            if (!assessmentsection || assessmentsection.length === 0) return;


            // Update assessmentsection
            const updatedAssessmentSection = assessmentsection.map((sec) => {
                if (sec.id !== sectionId) return sec;

                return {
                    ...sec,
                    assessmentSubSection1s: sec.assessmentSubSection1s.map((sub1) => {
                        if (sub1.id !== subsection1Id) return sub1;

                        return {
                            ...sub1,
                            assessmentSubSection2s: sub1.assessmentSubSection2s.map((sub2) => {
                                if (sub2.id !== subsection2Id) return sub2;

                                // Parse data1 for questions
                                const parsedData = JSON.parse(sub2.form.data1);
                                const updatedQuestion = { ...parsedData[questionIndex] };

                                // Update attachments (single or multiple)
                                updatedQuestion.attachments = uploadedFile

                                // Update metadata
                                updatedQuestion.modified_by = login_data.id;
                                updatedQuestion.modified_on = DateTime.utc();

                                // Save back to parsedData
                                parsedData[questionIndex] = updatedQuestion;

                                return {
                                    ...sub2,
                                    form: {
                                        ...sub2.form,
                                        datas: parsedData,
                                        data1: JSON.stringify(parsedData),
                                    },
                                };
                            }),
                        };
                    }),
                };
            });

            // Update state
            setAssessmentSection(updatedAssessmentSection);

            // Update selected section
            const newSection = updatedAssessmentSection.find((s) => s.id === sectionId);
            const newSubSection1 = newSection?.assessmentSubSection1s.find(
                (s1) => s1.id === subsection1Id
            );
            const newSubSection2 = newSubSection1?.assessmentSubSection2s.find(
                (s2) => s2.id === subsection2Id
            );

            if (newSubSection2) setSelectSection(newSubSection2);
            forceUpdate();

        } catch (error) {
            console.error("File upload failed: ", error);
        }
    };



    /**
 * Validate all questions in the assessmentsection structure.
 * Return { valid: boolean, message: string } so we can show a message if invalid.
 */
    function validateAssessment(assessmentsection) {
        for (const sectionItem of assessmentsection) {
            const sectionId = sectionItem.id;

            for (const sub1Item of sectionItem.assessmentSubSection1s || []) {
                const subsection1Id = sub1Item.id;

                for (const sub2Item of sub1Item.assessmentSubSection2s || []) {
                    const subsection2Id = sub2Item.id;

                    let parsedQuestions = [];
                    try {
                        parsedQuestions = JSON.parse(sub2Item.form?.data1 || "[]");
                    } catch (err) {
                        return {
                            valid: false,
                            message: "Invalid data structure found.",
                            location: { sectionId, subsection1Id, subsection2Id, questionIndex: 0 }
                        };
                    }

                    for (let questionIndex = 0; questionIndex < parsedQuestions.length; questionIndex++) {
                        const question = parsedQuestions[questionIndex];

                        // 1) Check question.required (or "required": true, or whatever your field is)
                        if (question.required) {
                            // Check based on question type
                            if (question.type === "radio-group") {
                                const selectedValue = question.values?.find((v) => v.selected)?.label;
                                if (!selectedValue) {
                                    return {
                                        valid: false,
                                        message: `Please select a response for "${question.label}".`,
                                        location: { sectionId, subsection1Id, subsection2Id, questionIndex }
                                    };
                                }

                            }
                            if (question.type === "textarea") {
                                if (!question.value?.trim()) {
                                    return {
                                        valid: false,
                                        message: `Please enter text for "${question.label}".`,
                                        location: { sectionId, subsection1Id, subsection2Id, questionIndex }
                                    };
                                }
                            }
                            if (question.type === "select") {
                                if (!question.value) {
                                    return {
                                        valid: false,
                                        message: `Please select a value for "${question.label}".`,
                                        location: { sectionId, subsection1Id, subsection2Id, questionIndex }
                                    };
                                }
                            }
                        }

                        // 2) Check isAttachmentMandatory
                        // if (question.isAttachmentMandatory) {
                        //     if (!question.attachments || question.attachments.length === 0) {
                        //         return {
                        //             valid: false,
                        //             message: `Attachment is required for "${question.label}".`,
                        //             location: { sectionId, subsection1Id, subsection2Id, questionIndex }
                        //         };
                        //     }
                        // }
                    }
                }
            }
        }

        // If we never fail, it's valid:
        return { valid: true, message: "All required fields are filled.", location: null };
    }
    // helper that produces a *stable*, *unique* token for every field
    const fieldId = (type, sec, s1, s2, q, opt = '') =>
        `${type}-${sec}-${s1}-${s2}-${q}${opt && `-${opt}`}`;


    const renderResponse = (question, index, auditId, section, subsection1, selectedsubsection2, formId, id) => {


        console.log(question)
        const highlightThis =
            highlightedQuestion &&
            highlightedQuestion.sectionId === section &&
            highlightedQuestion.subsection1Id === subsection1 &&
            highlightedQuestion.subsection2Id === selectedsubsection2 &&
            highlightedQuestion.questionIndex === index;



        const previousResponses = supplierResponseData.find(sec => sec.id === id)


        let previousResponse = null;

        if (previousResponses) {
            const subsection1Data = previousResponses.assessmentSubSection1s.find(sec => sec.id === subsection1)

            if (subsection1Data) {
                const subsection2Data = subsection1Data.assessmentSubSection2s.find(sub2 => sub2.id === selectedsubsection2);

                if (subsection2Data && subsection2Data.form?.data1) {
                    const parsedQuestions = JSON.parse(subsection2Data.form.data1);
                    // Use index as primary identifier, fallback to name matching
                    previousResponse = parsedQuestions[index] || parsedQuestions.find(q => q.name === question.name);
                }
            }
        }



        return (
            <>  <div key={`question-${section}-${subsection1}-${selectedsubsection2}-${index}-${question.id}`} className='questDisp row  m-0 p-0' style={{
                // If we should highlight, show a red border (or any style you prefer)
                border: highlightThis ? "2px solid red" : "none",
                padding: highlightThis ? "8px" : "0",

            }}>
                <div className="col-8 fs-16 fw-4">
                    <p id={`question-label-${section}-${subsection1}-${selectedsubsection2}-${index}-${question.id}`} style={{ color: question.isDedicated ? "red" : '#374151' }}>{question.label}

                        {question.tag && (
                            <span className={`badge ms-2 ${question.tag}`}>
                                {question.tag.toUpperCase()}
                            </span>
                        )}
                    </p>

                    {question.type === 'radio-group' ? (
                        <div className='grid m-0 p-0'>
                            {question.values.map((cb, cbind) => {
                                return (
                                    <div className="p-2 flex text-justify fs-14 fw-5 align-items-center" >
                                        <RadioButton
                                            inputId={`radio-${section}-${subsection1}-${selectedsubsection2}-${index}-${question.id}-${cbind}`}
                                            name={fieldId('rgGroup', section, subsection1,
                                                selectedsubsection2, index)} // same “name” for the set
                                            value={cb.value}
                                            disabled={!editable}
                                            onChange={(e) =>
                                                onRadioButtonSelected(
                                                    question,
                                                    cbind,
                                                    auditId,
                                                    section,
                                                    subsection1,
                                                    selectedsubsection2,
                                                    formId,
                                                    index
                                                )
                                            }
                                            checked={cb.selected || false} // Ensure `checked` reflects the updated state
                                        />
                                        <label htmlFor={`radio-${section}-${subsection1}-${selectedsubsection2}-${index}-${question.id}-${cbind}`} className="ml-2">{cb.label}</label>
                                    </div>
                                )
                            })}
                        </div>

                    ) :
                        question.type === 'select' ? <Dropdown disabled={!editable} placeholder={question.placeholder} options={question.values} style={{ width: '100%' }} optionLabel='label' optionValue="value" value={question.value} onChange={(e) => {
                            onChangeDropwdown(e.value,        // new textarea text
                                index,                 // question index
                                section,               // section id
                                subsection1,           // subsection1 id
                                selectedsubsection2)
                        }} />



                            : question.type === 'textarea' ? (
                                <InputTextarea
                                    id={`txt-${section}-${subsection1}-${selectedsubsection2}-${index}-${question.id}`}
                                    key={`txt-${section}-${subsection1}-${selectedsubsection2}-${index}-${question.id}`} // React key
                                    aria-labelledby={`question-label-${section}-${subsection1}-${selectedsubsection2}-${index}-${question.id}`}
                                    disabled={!editable}
                                    placeholder="Enter your response here"
                                    value={question.value}
                                    style={{ width: '100%', height: 120, overflow: 'auto' }}
                                    onChange={(e) => {
                                        onTextareaChange(
                                            e.target.value,        // new textarea text
                                            index,                 // question index
                                            section,               // section id
                                            subsection1,           // subsection1 id
                                            selectedsubsection2    // subsection2 id
                                        );
                                    }}
                                />


                            ) : question.type === 'attachment' ? (

                                <AttachmentAsIcon
                                    mandatory={question.isAttachmentMandatory}
                                    edit={editable ? 1 : 0}
                                    documents={question.attachments}
                                    refId={`ATT_${section}_${subsection1}_${selectedsubsection2}_${index}`}
                                    getFiles={(files) =>
                                        onFileUpload(files, index, section, subsection1, selectedsubsection2)
                                    }
                                    hint="Upload evidence"
                                />
                            )


                                : null}
                </div>
                <div className="col-2 flex justify-content-between" style={{ flexDirection: 'column' }}>
                    {question.isAttachmentMandatory && editable &&
                        <>

                            <div>

                                <AttachmentAsIcon mandatory={true} edit={1} refId={section + subsection1 + selectedsubsection2 + index + question.id} getFiles={(e) => {

                                    console.log(e);
                                    onFileUpload(
                                        e,       // new textarea text
                                        index,                 // question index
                                        section,               // section id
                                        subsection1,           // subsection1 id
                                        selectedsubsection2
                                    );
                                }} documents={question.attachments} hint={''} />


                            </div>

                        </>
                    }
                    {!editable && question?.attachments?.length &&
                        <AttachmentAsIcon mandatory={true} edit={0} getFiles={(e) => {
                            onFileUpload(
                                e,       // new textarea text
                                index,                 // question index
                                section,               // section id
                                subsection1,           // subsection1 id
                                selectedsubsection2
                            );
                        }} documents={question.attachments} hint={''} />



                    }
                    {question.type !== 'textarea' && ((!editable && question.comment) || editable) && <div style={{ color: '#315975' }} className="" onClick={(e) => { e.stopPropagation(); handleComment(question, index, section, subsection1, selectedsubsection2); }}>
                        <i className="pi pi-comment fs-14 fw-6 mr-2" style={{ marginRight: '5px', cursor: 'pointer' }}></i>
                        <span>{question.comment ? editable ? 'Update ' : 'View' : 'Add '} Comment</span>
                    </div>}
                </div>
                <div className="col-2 ">
                    <p style={{ fontWeight: 'bold', color: '#315975' }}>Supplier Response</p>
                    <div style={{ backgroundColor: "#f5f5f5", padding: "10px", borderRadius: "5px", minHeight: "50px" }}>

                        {question.type === 'radio-group' ?
                            (previousResponse && previousResponse.values
                                ? previousResponse.values.find(v => v.selected)?.label || "No previous response"
                                : "No previous response")
                            : question.type === 'textarea' ?
                                (previousResponse && previousResponse.value
                                    ? <p>{previousResponse.value}</p>
                                    : <p>No previous response</p>)
                                : ''
                        }

                        {/* Check if previousResponse exists before accessing comment */}
                        <p>{previousResponse?.comment ? previousResponse.comment : ''}</p>

                        {/* Check if previousResponse and attachments exist before accessing */}
                        {previousResponse?.attachments && previousResponse.attachments.length > 0 ? (
                            previousResponse.attachments.map(i => {
                                <div className="clr-navy grid m-0  p-0">
                                    <i className="pi pi-paperclip fs-14 fw-6 mr-2" />
                                    <label
                                        style={{ width: '80%' }}
                                        className='text-three-dot text-underline cur-pointer'
                                        onClick={() => { window.open(API.Docs + i.name) }}
                                    >
                                        {i.name}
                                    </label>
                                </div>
                            })

                        ) : (
                            <p>No attachments</p> // Show message when there are no attachments
                        )}
                    </div>
                </div>

            </div>

                {/* ─────────────  NC banner  ───────────── */}
                {question.nc?.raised && (
                    <div className="p-3 mt-2" style={{ border: '1px solid #e11d48', borderRadius: 4 }}>
                        <p className="m-0 fw-6" style={{ color: '#e11d48' }}>
                            NON-COMPLIANCE (NC) – Details
                        </p>

                        {/* 1. Applicable Law * */}
                        <Dropdown
                            className="w-full mt-3"
                            value={question.nc.applicableLaw}
                            options={lawsOption}
                            optionLabel="title"
                            optionValue="id"
                            filter
                            placeholder="Applicable law *"
                            disabled={!editable}
                            onChange={e =>
                                updateNCField(section, subsection1, selectedsubsection2, index,
                                    'applicableLaw', e.value)}
                        />

                        {/* 1a. Other law * (when id === 0) */}
                        {question.nc.applicableLaw === 0 && (
                            <InputText
                                className="w-full mt-3"
                                placeholder="Please mention name of Law *"
                                value={question.nc.otherLaw}
                                disabled={!editable}
                                onChange={e =>
                                    updateNCField(section, subsection1, selectedsubsection2, index,
                                        'otherLaw', e.target.value)}
                            />
                        )}

                        {/* 2. Short title * */}
                        <InputText
                            className="w-full mt-3"
                            placeholder="Provide a short title for this observation*"
                            value={question.nc.finding}
                            disabled={!editable}
                            onChange={e =>
                                updateNCField(section, subsection1, selectedsubsection2, index,
                                    'finding', e.target.value)}
                        />

                        {/* 3. Detailed description * */}
                        <InputTextarea
                            rows={3}
                            autoResize
                            className="w-full mt-3"
                            placeholder="Describe the observation in detail, where applicable, refer to the samples and supporting device for the finding*"
                            value={question.nc.description}
                            disabled={!editable}
                            onChange={e =>
                                updateNCField(section, subsection1, selectedsubsection2, index,
                                    'description', e.target.value)}
                        />

                        {/* 4. Evidence * when mandatory === true */}
                        <AttachmentAsIcon
                            mandatory={question.nc.mandatory}   // still enforced
                            edit={editable ? 1 : 0}
                            documents={question.nc.evidence}
                            refId={'NC_' + section + subsection1 + selectedsubsection2 + index + question.id}
                            getFiles={files =>
                                updateNCField(section, subsection1, selectedsubsection2, index,
                                    'evidence', files)}
                            hint="Upload evidence"
                        />
                    </div>
                )}


                {/* ─────────────  OFI / CP banner  ───────────── */}
                {question.ofi?.raised && (
                    <div className="p-3 mt-2" style={{ border: '1px solid #2563eb', borderRadius: 4 }}>
                        <p className="m-0 fw-6" style={{ color: '#2563eb' }}>
                            OPPORTUNITY FOR IMPROVEMENT (OFI / CP)
                        </p>

                        <InputText
                            className="w-full mt-3"
                            placeholder="Provide a short title for this observation*"
                            value={question.nc.finding}
                            disabled={!editable}
                            onChange={e =>
                                updateNCField(section, subsection1, selectedsubsection2, index,
                                    'finding', e.target.value)}
                        />

                        {/* 3. Detailed description * */}
                        <InputTextarea
                            rows={3}
                            autoResize
                            className="w-full mt-3"
                            placeholder="Describe the observation in detail, where applicable, refer to the samples and supporting device for the finding*"
                            value={question.nc.description}
                            disabled={!editable}
                            onChange={e =>
                                updateNCField(section, subsection1, selectedsubsection2, index,
                                    'description', e.target.value)}
                        />
                        <AttachmentAsIcon
                            mandatory={false}
                            edit={editable ? 1 : 0}
                            documents={question.ofi.evidence}
                            refId={'OFI_' + section + subsection1 + selectedsubsection2 + index + question.id}
                            getFiles={files =>
                                updateOFIField(section, subsection1, selectedsubsection2, index, 'evidence', files)}
                            hint="Upload evidence"
                        />
                    </div>
                )}

                < hr className="p-1 m-0" />
            </>
        )
    }


    const handleSubmit = () => {
    
        const { valid, message, location } = validateAssessment(assessmentsection);
    console.log(valid, message, location,result)
        if (!valid) {
            // Show an error message to the user (Swal, Toast, etc.)
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: message,
            });

            // location = { sectionId, subsection1Id, subsection2Id, questionIndex }
            // 1) Move the Stepper to the correct "section"
            const sectionIndex = assessmentsection.findIndex(
                (sec) => sec.id === location.sectionId
            );
            if (sectionIndex !== -1) {
                setActiveIndex(sectionIndex);  // or whatever function sets your stepper index
            }

            // 2) Expand the correct Accordion tab
            // You can find which sub1 is in the array:
            const sub1Index = assessmentsection[sectionIndex]?.assessmentSubSection1s?.findIndex(
                (s1) => s1.id === location.subsection1Id
            );
            // setActiveAccordionIndex(sub1Index) if you are using that logic

            // 3) Set selected sub-subsection
            const sub2Item = assessmentsection[sectionIndex]?.assessmentSubSection1s?.[sub1Index].assessmentSubSection2s?.find(
                (s2) => s2.id === location.subsection2Id
            );
            if (sub2Item) {
                setSelectedSubSection2(sub2Item.id);
                setSelectSection(sub2Item);
            }

            // 4) Optionally, highlight the question itself
            // For example, store a highlight state that renderResponse checks
            setHighlightedQuestion({
                sectionId: location.sectionId,
                subsection1Id: location.subsection1Id,
                subsection2Id: location.subsection2Id,
                questionIndex: location.questionIndex
            });

            return;
        }
        if ((result === 1 || result === 0) && auditId?.vendor?.code && auditId?.vendor?.id) {
            const requestBody = {
                type: 1,
                response: JSON.stringify(assessmentsection),
                modified_on: DateTime.utc(),
                modified_by: login_data.id,
                auditorMSIScore: grandTotalScore,
                submitted_on: DateTime.utc(),
                submitted_by: login_data.id,
                vendorCode: auditId?.vendor?.code,
                vendorId: auditId?.vendor?.id,
                userProfileId: admin_data.id
            };

            APIServices.post(API.AuditorAssessmentSubmission(auditId.id), requestBody)
                .then(response => {
                    Swal.fire("Success!", "Response Updated successfully.", "success").then(() => {
                        window.location.reload(); // Reloads the page after clicking OK
                    });
                    refresh();

                })
                .catch(error => {
                    console.log(error)
                    Swal.fire("Error!", "There was an issue submitting the response.", "error");
                });
        } else if (result === 2 && auditId?.vendor?.code && auditId?.vendor?.id && auditId?.auditorAssignmentSubmission?.id) {
            const requestBody = {

                modified_on: DateTime.utc(),
                modified_by: login_data.id,
                type: 1

            };

            APIServices.patch(API.AuditorAssessmentSubmission_Edit(auditId?.auditorAssignmentSubmission?.id), requestBody)
                .then(response => {
                    Swal.fire("Success!", "Response Updated successfully.", "success")
                    refresh();
                    closeModal(false)
                })
                .catch(error => {
                    console.log(error)
                    Swal.fire("Error!", "There was an issue submitting the response.", "error");
                });
        }
    }

    const handleProgress = () => {

        if ((result === 1 || result === 0) && auditId?.vendor?.code && auditId?.vendor?.id && subsectiondata.id) {
            const requestBody = {
                type: 0,
                response: JSON.stringify(assessmentsection),
                created_on: DateTime.utc(), // If you need a timestamp
                modified_on: DateTime.utc(),
                created_by: login_data.id,
                userProfileId: admin_data.id,
                auditorMSIScore: grandTotalScore,
                status: 0,
                submitted_on: null,
                supplierAssessmentAssignmentId: auditId.id,
                submitted_by: null,
                vendorCode: auditId?.vendor?.code,
                vendorId: auditId?.vendor?.id
            };

            APIServices.post(API.AuditorChecklistSubmission_Custom(subsectiondata.id), requestBody)
                .then(response => {
                    if (response?.data?.result) {
                        setSupplierId(response?.data?.id || null)
                        setAssessmentSection(JSON.parse(response?.data?.response || '[]'))
                        setResult(response?.data?.id ? 2 : 0)
                        Swal.fire("Success!", "Response Updated successfully.", "success")
                        refresh();
                    }

                })
                .catch(error => {
                    Swal.fire("Error!", "There was an issue submitting the response.", "error");
                });
        } else if (result === 2 && auditId?.vendor?.code && auditId?.vendor?.id && subsectiondata.id) {
            const requestBody = {
                submitted_on: null,
                submitted_by: null,
                response: JSON.stringify(assessmentsection),
                modified_on: DateTime.utc(),
                supplierAssessmentAssignmentId: auditId.id,
                modified_by: login_data.id,
                auditorMSIScore: grandTotalScore,
                type: 0

            };

            APIServices.post(API.AuditorChecklistSubmission_Custom(subsectiondata.id), requestBody)
                .then(response => {
                    if (response?.data?.result) {
                        setSupplierId(response?.data?.id || null)
                        setAssessmentSection(JSON.parse(response?.data?.response || '[]'))
                        setResult(response?.data?.id ? 2 : 0)
                    }
                    refresh();
                    Swal.fire("Success!", "Response Updated successfully.", "success")
                })
                .catch(error => {


                    Swal.fire("Error!", "There was an issue submitting the response.", "error");
                });
        } else {

            Swal.fire("Error!", "There was an issue with saving section response.", "error");
            console.log(auditId, result)
        }
    };

    // Auto save function
    const performAutoSave = useCallback(() => {
        // Only perform auto save if editable is true
        if (!editable) {
            console.log("Auto save skipped - not editable");
            return;
        }

        // Debug logging to understand what data is available
        console.log("Auto save check - Data availability:", {
            vendorCode: auditId?.vendor?.code,
            vendorId: auditId?.vendor?.id,
            subsectionId: subsectiondata?.id,
            assessmentSectionLength: assessmentsection?.length,
            activeIndex: activeIndex
        });

        if (!auditId?.vendor?.code || !auditId?.vendor?.id) {
            console.log("Auto save skipped - missing vendor data");
            return;
        }

        // Check if we have assessmentsection data to save
        if (!assessmentsection || assessmentsection.length === 0) {
            console.log("Auto save skipped - no assessment section data available");
            return;
        }

        // Use the current active section, or first section if activeIndex is not valid
        let currentSubsectionData = null;
        if (subsectiondata?.id) {
            currentSubsectionData = subsectiondata;
            console.log("Using subsectiondata for auto save:", currentSubsectionData?.title, currentSubsectionData?.id);
        } else if (activeIndex >= 0 && activeIndex < assessmentsection.length) {
            currentSubsectionData = assessmentsection[activeIndex];
            console.log("Using active section for auto save:", currentSubsectionData?.title, currentSubsectionData?.id);
        } else {
            // Use the first section as fallback
            currentSubsectionData = assessmentsection[0];
            console.log("Using first section as fallback for auto save:", currentSubsectionData?.title, currentSubsectionData?.id);
        }

        if (!currentSubsectionData?.id) {
            console.log("Auto save skipped - no valid section data available");
            return;
        }

        setIsAutoSaving(true);
        console.log("Auto save triggered at", new Date().toLocaleTimeString(), "for section:", currentSubsectionData?.title);

        if ((result === 1 || result === 0)) {
            const requestBody = {
                type: 0,
                response: JSON.stringify(assessmentsection),
                created_on: DateTime.utc(),
                modified_on: DateTime.utc(),
                created_by: login_data.id,
                userProfileId: admin_data.id,
                auditorMSIScore: grandTotalScore,
                status: 0,
                submitted_on: null,
                supplierAssessmentAssignmentId: auditId.id,
                submitted_by: null,
                vendorCode: auditId?.vendor?.code,
                vendorId: auditId?.vendor?.id
            };

            APIServices.post(API.AuditorChecklistSubmission_Custom(currentSubsectionData.id), requestBody)
                .then(response => {
                    if (response?.data?.result) {
                        setSupplierId(response?.data?.id || null);
                        // setAssessmentSection(JSON.parse(response?.data?.response || '[]'));
                        setResult(response?.data?.id ? 2 : 0);
                        setLastSaveTime(new Date());
                        console.log("Auto save successful - performing auto retrieve");

                        // Auto retrieve after successful save
                        // return APIServices.post(API.AuditorChecklistSubmission_Recent, {
                        //     supplierAssessmentAssignmentId: auditId.id,
                        //     sectionId: subsectiondata.id
                        // });
                    }
                    return null;
                })
                .then(retrieveRes => {
                    if (retrieveRes?.data?.result === 2) {
                        setSupplierId(retrieveRes?.data?.id || null);
                        const retrievedData = JSON.parse(retrieveRes?.data?.response || '[]');
                        // setAssessmentSection(retrievedData);
                        setResult(2);
                        console.log("Auto retrieve successful: Data refreshed after save");
                    } else if (retrieveRes) {
                        console.log("Auto retrieve: No recent data found");
                    }
                    setIsAutoSaving(false);
                })
                .catch(error => {
                    console.error("Auto save/retrieve failed:", error);
                    setIsAutoSaving(false);
                });
        } else if (result === 2) {
            const requestBody = {
                submitted_on: null,
                submitted_by: null,
                response: JSON.stringify(assessmentsection),
                modified_on: DateTime.utc(),
                supplierAssessmentAssignmentId: auditId.id,
                modified_by: login_data.id,
                auditorMSIScore: grandTotalScore,
                type: 0
            };

            APIServices.post(API.AuditorChecklistSubmission_Custom(currentSubsectionData.id), requestBody)
                .then(response => {
                    if (response?.data?.result) {
                        setSupplierId(response?.data?.id || null);
                        // setAssessmentSection(JSON.parse(response?.data?.response || '[]'));
                        setResult(response?.data?.id ? 2 : 0);
                        setLastSaveTime(new Date());
                        console.log("Auto save successful - performing auto retrieve");

                        // Auto retrieve after successful save
                        // return APIServices.post(API.AuditorChecklistSubmission_Recent, {
                        //     supplierAssessmentAssignmentId: auditId.id,
                        //     sectionId: subsectiondata.id
                        // });
                    }
                    return null;
                })
                .then(retrieveRes => {
                    if (retrieveRes?.data?.result === 2) {
                        setSupplierId(retrieveRes?.data?.id || null);
                        const retrievedData = JSON.parse(retrieveRes?.data?.response || '[]');
                        // setAssessmentSection(retrievedData);
                        setResult(2);
                        console.log("Auto retrieve successful: Data refreshed after save");
                    } else if (retrieveRes) {
                        console.log("Auto retrieve: No recent data found");
                    }
                    setIsAutoSaving(false);
                })
                .catch(error => {
                    console.error("Auto save/retrieve failed:", error);
                    setIsAutoSaving(false);
                });
        } else {
            setIsAutoSaving(false);
        }
    }, [assessmentsection, result, auditId, subsectiondata, login_data, admin_data, grandTotalScore, editable, activeIndex]);

    // Timer countdown function
    const startTimer = useCallback(() => {
        setAutoSaveTimer(30);

        if (timerIntervalRef.current) {
            clearInterval(timerIntervalRef.current);
        }

        timerIntervalRef.current = setInterval(() => {
            setAutoSaveTimer(prev => {
                if (prev <= 1) {
                    return 30; // Reset to 30 when it reaches 0
                }
                return prev - 1;
            });
        }, 1000);
    }, []);

    // Store the latest performAutoSave function in a ref
    const performAutoSaveRef = useRef(performAutoSave);
    performAutoSaveRef.current = performAutoSave;

    // Auto save setup
    useEffect(() => {
        // Clear existing intervals
        if (autoSaveIntervalRef.current) {
            clearInterval(autoSaveIntervalRef.current);
        }
        if (timerIntervalRef.current) {
            clearInterval(timerIntervalRef.current);
        }

        // Start auto save if editable is true and we have assessment data
        if (editable && assessmentsection && assessmentsection.length > 0) {
            console.log("Setting up auto-save interval - editable is true and assessment data available");
            console.log("Assessment sections available:", assessmentsection.length);

            // Start timer countdown
            startTimer();

            // Set up auto save interval (30 seconds)
            autoSaveIntervalRef.current = setInterval(() => {
                console.log("30-second auto-save interval triggered");
                performAutoSaveRef.current();
            }, 30000);
            console.log("Auto-save interval set up successfully");
        } else {
            console.log("Auto-save interval not set up - conditions not met:", {
                editable: editable,
                hasAssessmentData: assessmentsection && assessmentsection.length > 0,
                assessmentSectionLength: assessmentsection?.length || 0
            });
        }

        // Cleanup on unmount
        return () => {
            if (autoSaveIntervalRef.current) {
                clearInterval(autoSaveIntervalRef.current);
            }
            if (timerIntervalRef.current) {
                clearInterval(timerIntervalRef.current);
            }
        };
    }, [startTimer, editable, assessmentsection]);

    const leftToolbarTemplate = () => {
        return (
            <React.Fragment>
                <div className="col-12 flex justify-content-end">
                    <Button
                        label="Record Observation"
                        icon="pi pi-plus"
                        className=" mr-2"
                        onClick={openNew}
                    />
                </div>
            </React.Fragment>
        );
    };

    // ** FileUpload Handlers **

    // When user selects files (before upload)
    const handleFileUpload = async (event) => {
        console.log(event[0])
        const selectedFiles = event[0];


        const formData = new FormData();

        formData.append('file', selectedFiles);


        try {
            APIServices.post(API.FilesUpload, formData, {
                headers: {
                    'content-type': 'multipart/form-data'

                }
            }).then((res) => {
                Swal.fire({
                    title: 'Upload Successful!',
                    text: `${res?.data?.files?.length} file(s) uploaded successfully.`,
                    icon: 'success',
                    confirmButtonText: 'OK',
                });
                setCurrentFinding((prev) => ({
                    ...prev,
                    auditorAttachments: [...prev.auditorAttachments, ...res.data.files], // Store uploaded file info
                }));
            })





        } catch (error) {
            console.error('File upload error:', error);
            Swal.fire({
                title: 'Upload Failed!',
                text: 'An error occurred while uploading. Please try again.',
                icon: 'error',
                confirmButtonText: 'OK',
            });
        }
    };

    const handleFileRemove = (fileIndex) => {
        setCurrentFinding((prev) => ({
            ...prev,
            auditorAttachments: prev.auditorAttachments.filter((_, index) => index !== fileIndex), // Remove by index
        }));

        Swal.fire({
            title: 'Deleted!',
            text: 'File has been removed successfully.',
            icon: 'success',
            confirmButtonText: 'OK',
        });
    };


    // Custom upload handler (if user clicks "Upload" in FileUpload)
    const onFileUploadHandler = (event) => {
        // In a real app, you might upload to backend here.
        // For now, just store them in local state (if not already).
        setCurrentFinding((prev) => ({
            ...prev,
            auditorAttachments: [...prev.files, ...event.files],
        }));
        // Clear the file queue so we don’t double-store them
        event.options.clear();
    };
    const onFileUploadSuccess = (event) => {

        setCurrentFinding((prev) => ({
            ...prev,
            auditorAttachments: event,
        }));
        Swal.fire({
            title: 'Upload Successful!',
            text: 'Your files have been uploaded successfully.',
            icon: 'success',
            confirmButtonText: 'OK',
        });
    };
    const getNoOfCritical = useMemo(() => {
        try {
            const criticalQuestions = assessmentsection.flatMap(x =>
                x?.assessmentSubSection1s?.flatMap(y =>
                    y?.assessmentSubSection2s?.flatMap(z => {
                        try {
                            return JSON.parse(z?.form?.data1 || '[]');
                        } catch (error) {
                            console.error('Error parsing data1 in getNoOfCritical:', error);
                            return [];
                        }
                    })
                ) || []
            ).filter(x => x?.isDedicated && x.type === "radio-group")
                .filter(x => x?.values?.find(y => y?.selected && y?.label?.trim().toLowerCase() === 'yes'));

            return criticalQuestions.length || 0;
        } catch (error) {
            console.error('Error in getNoOfCritical:', error);
            return 0;
        }
    }, [assessmentsection]);
    const onFileUploadError = (event) => {
        Swal.fire({
            title: 'Upload Failed!',
            text: 'An error occurred while uploading. Please try again.',
            icon: 'error',
            confirmButtonText: 'OK',
        });
    };
    const categoryTemplate = (rowData) => {
        return categoryOptions.find(x => x.id === rowData.categoryOfFinding)?.label || 'NA'
    }
    const complianceTemplate = (rowData) => {
        return nonComplianceOptions.find(x => x.id === rowData.nonComplianceType)?.label || 'NA'
    }
    const lawTemplate = (rowData) => {
        return rowData?.applicableLaw === 0 ? rowData.otherLaw : lawsOption.find(x => x.id === rowData.applicableLaw)?.title || 'NA'
    }
    // We already have a getDate function, so we'll use that one

    // Function to export data to Excel
    const exportToExcel = () => {
        try {
            // Create workbook
            const wb = XLSX.utils.book_new();

            // Create summary sheet (Sheet 1)
            const summaryData = [
                ['Self-assessment Due Date', getDate(auditId?.assessmentEndDate, 'dd MMM yyyy')],
                ['Self-assessment Submitted Date', auditId?.supplierAssignmentSubmission?.submitted_on ? getDate(auditId?.supplierAssignmentSubmission?.submitted_on, 'dd MMM yyyy') : 'Not Submitted'],
                ['Status', auditId?.auditorAssignmentSubmission ? auditId?.auditorAssignmentSubmission?.type ? auditId?.auditorAssignmentSubmission?.type === 2 ? "Approved" : auditId?.auditorAssignmentSubmission?.type === 12 ? "Under Approval" : auditId?.auditorAssignmentSubmission?.type === 21 ? "Under Approval Initiate" : auditId?.auditorAssignmentSubmission?.type === 1 ? "In Progress" : 'Not Started' : 'Not Started' : 'Not Started'],
                ['Category', categoryList.find(x => x.value === auditId?.vendor?.supplierCategory)?.name || 'NA'],
                ['Vendor Code', auditId?.vendor?.code || 'NA'],
                ['No. Of Critical Regulatory Gaps Identified', getNoOfCritical.toString()],
                ['MSI Self-Assessment Score', auditId?.supplierAssignmentSubmission?.supplierMSIScore?.toString() || '-'],
                ['MSI Audit Score', grandTotalScore.toFixed(2)]
            ];

            // Create summary worksheet
            const summaryWs = XLSX.utils.aoa_to_sheet(summaryData);

            // Add summary worksheet to workbook
            XLSX.utils.book_append_sheet(wb, summaryWs, 'Summary');

            // Create a sheet for each section in the stepper
            assessmentsection?.sort((a, b) => a.order - b.order).forEach(section => {
                // Data for this section
                const sectionData = [];

                // Add section title as header
                sectionData.push([section.title]);
                sectionData.push([]);  // Empty row for spacing

                // Process each subsection
                section.assessmentSubSection1s?.sort((a, b) => a.order - b.order).forEach(subsection1 => {
                    // Add subsection title
                    sectionData.push([subsection1.title]);

                    // Process each sub-subsection
                    subsection1.assessmentSubSection2s?.sort((a, b) => a.order - b.order).forEach(subsection2 => {
                        // Add sub-subsection title
                        sectionData.push(['', subsection2.title]);

                        // Add headers for questions
                        sectionData.push(['', 'Question', 'Auditor Response', 'Supplier Response', 'Comment']);

                        // Add questions and responses
                        if (subsection2.form?.data1) {
                            try {
                                const questions = JSON.parse(subsection2.form.data1);

                                questions.forEach(question => {
                                    let auditorResponse = '';
                                    let supplierResponse = '';

                                    // Find supplier response for this question
                                    const supplierQuestion = findSupplierResponse(question, section.id, subsection1.id, subsection2.id);

                                    // Get auditor response based on question type
                                    if (question.type === 'radio-group') {
                                        const selectedValue = question.values?.find(v => v.selected);
                                        auditorResponse = selectedValue ? selectedValue.label : '';

                                        // Get supplier response
                                        if (supplierQuestion && supplierQuestion.values) {
                                            const supplierSelectedValue = supplierQuestion.values.find(v => v.selected);
                                            supplierResponse = supplierSelectedValue ? supplierSelectedValue.label : '';
                                        }
                                    } else if (question.type === 'textarea') {
                                        auditorResponse = question.value || '';
                                        supplierResponse = supplierQuestion ? supplierQuestion.value || '' : '';
                                    } else if (question.type === 'select') {
                                        auditorResponse = question.value || '';
                                        supplierResponse = supplierQuestion ? supplierQuestion.value || '' : '';
                                    }

                                    // Add question data
                                    sectionData.push([
                                        '',
                                        question.label,
                                        auditorResponse,
                                        supplierResponse,
                                        question.comment || ''
                                    ]);
                                });
                            } catch (error) {
                                console.error('Error parsing questions:', error);
                            }
                        }

                        // Add empty row for spacing
                        sectionData.push([]);
                    });

                    // Add empty row for spacing between subsections
                    sectionData.push([]);
                });

                // Create worksheet for this section
                const sectionWs = XLSX.utils.aoa_to_sheet(sectionData);

                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, sectionWs, section.title.substring(0, 30)); // Limit sheet name length
            });

            // Add findings sheet
            if (findings.length > 0) {
                const findingsData = [
                    ['Category', 'Non-Compliance Type', 'Applicable Law', 'Finding', 'Description']
                ];

                findings.forEach(finding => {
                    findingsData.push([
                        categoryOptions.find(x => x.id === finding.categoryOfFinding)?.label || 'NA',
                        nonComplianceOptions.find(x => x.id === finding.nonComplianceType)?.label || 'NA',
                        finding.applicableLaw === 0 ? finding.otherLaw : lawsOption.find(x => x.id === finding.applicableLaw)?.title || 'NA',
                        finding.finding || '',
                        finding.description || ''
                    ]);
                });

                const findingsWs = XLSX.utils.aoa_to_sheet(findingsData);
                XLSX.utils.book_append_sheet(wb, findingsWs, 'Findings');
            }

            // Generate Excel file
            const fileName = `MSI_Audit_${auditId?.vendor?.code || 'Unknown'}_${DateTime.now().toFormat('yyyyMMdd_HHmmss')}.xlsx`;
            XLSX.writeFile(wb, fileName);

            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Excel Export Successful',
                text: `File "${fileName}" has been downloaded.`,
                timer: 3000
            });
        } catch (error) {
            console.error('Error exporting to Excel:', error);

            // Show error message
            Swal.fire({
                icon: 'error',
                title: 'Export Failed',
                text: 'An error occurred while exporting to Excel. Please try again.'
            });
        }
    };
    // ⬇️ place this just after the stepss useMemo (or inside it if you prefer)
    const allSectionsComplete = useMemo(() => {
        if (!assessmentsection.length) return false;

        // every sub-sub-section must pass your existing `checkQuestionary`
        return assessmentsection.every(section =>
            section.assessmentSubSection1s.every(sub1 =>
                sub1.assessmentSubSection2s.every(sub2 =>
                    checkQuestionary(sub2.form?.data1 || '[]')
                )
            )
        );
    }, [assessmentsection, checkQuestionary]);

    // Helper function to find supplier response for a question
    const findSupplierResponse = (question, sectionId, subsection1Id, subsection2Id) => {
        if (!supplierResponseData || supplierResponseData.length === 0) {
            return null;
        }

        try {
            // Find the corresponding section in supplier response
            const section = supplierResponseData.find(s => s.id === sectionId);
            if (!section) return null;

            // Find the corresponding subsection1
            const subsection1 = section.assessmentSubSection1s?.find(s1 => s1.id === subsection1Id);
            if (!subsection1) return null;

            // Find the corresponding subsection2
            const subsection2 = subsection1.assessmentSubSection2s?.find(s2 => s2.id === subsection2Id);
            if (!subsection2 || !subsection2.form?.data1) return null;

            // Parse the data and find the question with the same name/label
            const questions = JSON.parse(subsection2.form.data1);
            return questions.find(q => q.name === question.name || q.label === question.label);
        } catch (error) {
            console.error('Error finding supplier response:', error);
            return null;
        }
    };

    // const stepss = assessmentsection
    //     ?.sort((a, b) => a.order - b.order)
    //     .map((section) => {
    //         return {
    //             label: section.title,
    //             id: section.id,
    //             completed: section.completed, // All questions answered
    //             hasProgress: section.hasProgress, // Some questions answered
    //         };
    //     }) || [];

    const stepss = useMemo(() => {
        // helper ⇒ is every sub-section in this Section 100 % complete?
        const isSubSectionDone = (sub1) =>
            sub1.assessmentSubSection2s.every(sub2 =>
                checkQuestionary(sub2.form?.data1 || "[]")
            );

        // helper ⇒ has **any** progress been made in this Section?
        const hasAnyProgress = (sub1) =>
            sub1.assessmentSubSection2s.some(sub2 =>
                checkQuestionsStatus(JSON.parse(sub2.form?.data1 || "[]")).hasProgress
            );

        return (
            assessmentsection
                ?.sort((a, b) => a.order - b.order)
                .map(section => {
                    const allDone = section.assessmentSubSection1s.every(isSubSectionDone);
                    const someProgress = section.assessmentSubSection1s.some(hasAnyProgress);

                    return {
                        label: section.title,
                        id: section.id,
                        completed: allDone,                // → green
                        hasProgress: !allDone && someProgress // → orange
                    };
                }) || []
        );
    }, [assessmentsection, checkQuestionary]);


    // Memoize parsed questions to avoid JSON.parse on every render
    const parsedQuestions = useMemo(() => {
        if (!selectSection?.form?.data1) return [];
        try {
            return JSON.parse(selectSection.form.data1);
        } catch (error) {
            console.error('Error parsing questions data:', error);
            return [];
        }
    }, [selectSection?.form?.data1]);

    // Memoize questionary and subsection status to avoid recalculating on every render
    const { questionaryStatus, subsectionStatus } = useMemo(() => {
        const questionaryMap = new Map();
        const subsectionMap = new Map();

        assessmentsection.forEach(section => {
            section?.assessmentSubSection1s?.forEach(subsection1 => {
                subsection1?.assessmentSubSection2s?.forEach(subsection2 => {
                    try {
                        const isComplete = checkQuestionary(subsection2?.form?.data1 || '[]');
                        questionaryMap.set(subsection2.id, isComplete);
                    } catch (error) {
                        console.error('Error checking questionary status:', error);
                        questionaryMap.set(subsection2.id, false);
                    }
                });

                // Check subsection1 status
                try {
                    const isSubsectionComplete = checkSubSection1(subsection1);
                    subsectionMap.set(subsection1.id, isSubsectionComplete);
                } catch (error) {
                    console.error('Error checking subsection status:', error);
                    subsectionMap.set(subsection1.id, false);
                }
            });
        });

        return {
            questionaryStatus: questionaryMap,
            subsectionStatus: subsectionMap
        };
    }, [assessmentsection, checkQuestionary, checkSubSection1]);

    return (
        <>
            {/* Auto Save Timer Display - Only show when editable */}
            {editable && (
                <div className="row mb-2" style={{ padding: '10px 10px 0 10px' }}>
                    <div className="col-12 d-flex justify-content-between align-items-center" style={{
                        backgroundColor: '#f8f9fa',
                        padding: '10px 15px',
                        borderRadius: '5px',
                        border: '1px solid #dee2e6'
                    }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                <i className="pi pi-clock" style={{ color: '#007bff', fontSize: '16px' }} />
                                <span style={{ fontWeight: '600', color: '#495057' }}>
                                    Next Auto Save: {autoSaveTimer}s
                                </span>
                            </div>

                            {isClosing && (
                                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                    <i className="pi pi-spin pi-spinner" style={{ color: '#dc3545', fontSize: '14px' }} />
                                    <span style={{ color: '#dc3545', fontSize: '14px' }}>Saving & Closing...</span>
                                </div>
                            )}

                            {isStepperSwitching && !isClosing && (
                                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                    <i className="pi pi-spin pi-spinner" style={{ color: '#007bff', fontSize: '14px' }} />
                                    <span style={{ color: '#007bff', fontSize: '14px' }}>Switching Section...</span>
                                </div>
                            )}

                            {isAutoSaving && !isStepperSwitching && !isClosing && (
                                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                    <i className="pi pi-spin pi-spinner" style={{ color: '#28a745', fontSize: '14px' }} />
                                    <span style={{ color: '#28a745', fontSize: '14px' }}>Saving & Retrieving...</span>
                                </div>
                            )}

                            {lastSaveTime && !isAutoSaving && !isStepperSwitching && (
                                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                    <i className="pi pi-check-circle" style={{ color: '#28a745', fontSize: '14px' }} />
                                    <span style={{ color: '#6c757d', fontSize: '14px' }}>
                                        Last saved: {lastSaveTime.toLocaleTimeString()}
                                    </span>
                                </div>
                            )}
                        </div>

                        <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
                            {/* <Button
                                label="Test Auto Save"
                                icon="pi pi-save"
                                className="p-button-sm p-button-outlined"
                                onClick={() => {
                                    console.log("Manual auto-save test triggered");
                                    performAutoSaveRef.current();
                                }}
                                style={{ fontSize: '12px', padding: '5px 10px' }}
                            /> */}
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                <i className="pi pi-info-circle" style={{ color: '#17a2b8', fontSize: '14px' }} />
                                <span style={{ color: '#6c757d', fontSize: '12px' }}>
                                    Auto save every 30 seconds with auto retrieve | Click stepper for manual retrieve
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            <div className="row mb-3" style={{ padding: '10px' }}>
                <div className="col-12 d-flex justify-content-end mb-3">
                    <Button
                        label="Download Excel"
                        icon="pi pi-file-excel"
                        className="p-button-success"
                        onClick={exportToExcel}
                    />
                </div>
                {editable && result === 2 && <div className="col-12 flex justify-content-end">
                    <Button label='View Report' icon='pi pi-eye' className='mr-2' onClick={viewReport} />

                </div>}
                <div className="col-md-6 p-2">
                    <p className="obs-title m-0">Vendor Name</p>
                    <p className="obs-content"> {auditId?.vendor?.supplierName || 'NA'}</p>
                </div>
                <div className="col-md-6 p-2">
                    <p className="obs-title m-0">Self-assessment Due Date</p>
                    <p className="obs-content">
                        {getDate(auditId?.assessmentEndDate, 'dd MMM yyyy')}
                    </p>
                </div>
                <div className="col-md-6 p-2">
                    <p className="obs-title m-0">Self-assessment Submitted Date</p>
                    <p className="obs-content">
                        {auditId?.supplierAssignmentSubmission?.submitted_on ? getDate(auditId?.supplierAssignmentSubmission?.submitted_on, 'dd MMM yyyy') : 'Not Submitted'}
                    </p>
                </div>
                <div className="col-md-6 p-2">
                    <p className="obs-title m-0">Status</p>
                    <Tag style={{ width: 'unset' }} className={auditId?.auditorAssignmentSubmission ? auditId?.auditorAssignmentSubmission?.type === 2 ? "status-tag-green" : "status-tag-orange" : 'status-tag-red'} >{auditId?.auditorAssignmentSubmission ? auditId?.auditorAssignmentSubmission?.type ? auditId?.auditorAssignmentSubmission?.type === 2 ? "Approved" : auditId?.auditorAssignmentSubmission?.type === 12 ? "Under Approval" : auditId?.auditorAssignmentSubmission?.type === 21 ? "Under Approval Initiate" : auditId?.auditorAssignmentSubmission?.type === 1 ? "In Progress" : 'Not Started' : 'Not Started' : 'Not Started'}</Tag>


                </div>
                <div className="col-md-6 p-2">
                    <p className="obs-title m-0">Category</p>

                    <p className="obs-content">
                        {categoryList.find(x => x.value === auditId?.vendor?.supplierCategory)?.name || 'NA'}


                    </p>
                </div>

                <div className="col-md-6 p-2">
                    <p className="obs-title m-0">Vendor Code</p>
                    <p className="obs-content"> {auditId?.vendor?.code || 'NA'}</p>
                </div>

                <div className="col-md-6 p-2">
                    <p className="obs-title m-0">No. Of Critical Regulatory Gaps Identified</p>
                    <p className="obs-content">
                        {getNoOfCritical}
                    </p>
                </div>

                <div className="col-md-6 p-2">
                    <p className="obs-title m-0">MSI Self-Assessment Score</p>
                    <p className="obs-content">
                        {auditId?.supplierAssignmentSubmission?.supplierMSIScore ? auditId.supplierAssignmentSubmission.supplierMSIScore.toFixed(2) : '-'}
                    </p>
                </div>
                <div className="col-md-6 p-2">
                    <p className="obs-title m-0">MSI Audit Score</p>
                    <p className="obs-content">
                        {grandTotalScore.toFixed(2)}
                    </p>
                </div>
                {/* Reviewer Comments Section */}
                {auditId?.auditorAssignmentSubmission?.reject && auditId?.auditorAssignmentSubmission?.rejectionComments && Array.isArray(auditId.auditorAssignmentSubmission.rejectionComments) && auditId.auditorAssignmentSubmission.rejectionComments.length > 0 && (
                    <div className="col-12 p-2">
                        <p className="obs-title m-0 mb-2">Rejection Comments</p>
                        <div className="p-3" style={{ backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
                            {auditId.auditorAssignmentSubmission.rejectionComments.map((comment, index) => (
                                <div key={index} className="mb-3 p-2" style={{ borderBottom: index < auditId.auditorAssignmentSubmission.rejectionComments.length - 1 ? '1px solid #dee2e6' : 'none' }}>
                                    <div className="mb-2" style={{ fontWeight: 'bold', color: '#495057' }}>
                                        {comment.user_type === 3 ? 'Approver' :
                                            comment.user_type === 2 ? 'Reviewer 2' :
                                                comment.user_type === 1 ? 'Reviewer 1' : 'Unknown'}
                                    </div>
                                    <div className="mb-2" style={{ whiteSpace: 'pre-wrap' }}>{comment.remarks}</div>
                                    <div style={{ fontSize: '0.85rem', color: '#6c757d' }}>
                                        {comment.created_on ? DateTime.fromISO(comment.created_on, { zone: 'utc' }).toLocal().toFormat('dd-LLL-yyyy HH:mm') : ''}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
                {!auditId?.auditorAssignmentSubmission?.reject && auditId?.auditorAssignmentSubmission?.approverComments && Array.isArray(auditId.auditorAssignmentSubmission.approverComments) && auditId.auditorAssignmentSubmission.approverComments.length > 0 && (
                    <div className="col-12 p-2">
                        <p className="obs-title m-0 mb-2">Review Comments</p>
                        <div className="p-3" style={{ backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
                            {auditId.auditorAssignmentSubmission.approverComments.map((comment, index) => (
                                <div key={index} className="mb-3 p-2" style={{ borderBottom: index < auditId.auditorAssignmentSubmission.approverComments.length - 1 ? '1px solid #dee2e6' : 'none' }}>
                                    <div className="mb-2" style={{ fontWeight: 'bold', color: '#495057' }}>
                                        {comment.user_type === 3 ? 'Approver' :
                                            comment.user_type === 2 ? 'Reviewer 2' :
                                                comment.user_type === 1 ? 'Reviewer 1' : 'Unknown'}
                                    </div>
                                    <div className="mb-2" style={{ whiteSpace: 'pre-wrap' }}>{comment.remarks}</div>
                                    <div style={{ fontSize: '0.85rem', color: '#6c757d' }}>
                                        {comment.created_on ? DateTime.fromISO(comment.created_on, { zone: 'utc' }).toLocal().toFormat('dd-LLL-yyyy HH:mm') : ''}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </div>

            {assessmentsection.length ?

                <Tab.Container id="left-tabs-example" className="mt-3 audit-tab" activeKey={assessmentsection?.[activeIndex]?.id} >
                    <Row style={{ marginTop: 20 }}>
                        <div style={{ width: '90%', margin: '0 auto' }}>
                            <Stepper
                                steps={stepss}
                                onClickStep={handleStepClick}
                                activeBGColor="green"
                                labelColor={labelColor}
                                activeIndex={activeIndex} // Current active step index
                                isLoading={isStepperSwitching}
                            />
                        </div>

                        <Col sm={12}>

                            <Tab.Content>
                                {assessmentsection.map((section, index) => {

                                    return (<Tab.Pane eventKey={section.id} key={section.id}>
                                        <label className="mb-4" style={{ fontSize: 14, fontWeight: 'bold' }}> Expand each of the sections below, click on the sub-section and provide your responses to all the check points. Where required, attach relevant documents. Please note that check points in red are considered critical questions from TVS Motors perspective and require documentary evidence.</label>

                                        <Accordion activeIndex={activeAccordionIndex}
                                            onTabChange={(e) => {
                                                setActiveAccordionIndex(e.index);
                                                // RESET your sub-subsection selection so you don't see old data
                                                setSelectedSubSection2(null);
                                                setSelectSection(null);
                                            }}>
                                            {section?.assessmentSubSection1s?.sort((a, b) => { return a.order - b.order }, 0).map((subsection1) => {
                                                return (
                                                    <AccordionTab key={subsection1.id} header={`${subsection1.title} - ${subsection1.totalScore || 0}`} headerStyle={{ borderLeft: '5px solid ' + (subsectionStatus.get(subsection1.id) ? 'green' : '#F59E0B') }} onClick={() => setSelectSubSection1(subsection1)}>
                                                        {
                                                            <>
                                                                <Nav variant="pills" className="flex-row custom-nav">
                                                                    {subsection1?.assessmentSubSection2s?.sort((a, b) => { return a.order - b.order }, 0).map(subsection2 => (
                                                                        <Nav.Item key={subsection2.id}>
                                                                            <Nav.Link eventKey={subsection2.id} style={{ borderLeft: '5px solid ' + (questionaryStatus.get(subsection2.id) ? 'green' : '#F59E0B') }} onClick={(ev) => { setSelectedSubSection2(subsection2.id); setFormId(subsection2?.formId); setSelectSection(subsection2); }}  >

                                                                                {subsection2.title} - {subsection2?.form?.score || 0}
                                                                            </Nav.Link>
                                                                        </Nav.Item>
                                                                    ))}
                                                                </Nav>




                                                                {selectedsubsection2 && selectSection && (
                                                                    <div>
                                                                        {selectSection.form?.data1 && (
                                                                            <div>
                                                                                {/* Parse and iterate over data1 */}
                                                                                {parsedQuestions.map((question, index) => (
                                                                                    <div key={index}>
                                                                                        {renderResponse(
                                                                                            question,
                                                                                            index,
                                                                                            auditId.id,
                                                                                            section.id,
                                                                                            subsection1.id,
                                                                                            selectedsubsection2,
                                                                                            formId,
                                                                                            assessmentsection?.[activeIndex]?.id
                                                                                        )}


                                                                                    </div>


                                                                                ))}
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                )}


                                                                {/* <Toolbar className="mb-4" right={auditId?.auditorAssignmentSubmission?.type ? <></> : leftToolbarTemplate} ></Toolbar> */}

                                                                {/* <DataTable value={findings} responsiveLayout="scroll">
                                                                    <Column field="categoryOfFinding" header="Category" body={categoryTemplate}></Column>
                                                                    <Column field="nonComplianceType" header="Non-Compliance Type" body={complianceTemplate}></Column>
                                                                    <Column field="applicableLaw" header="Applicable Law" body={lawTemplate}></Column>
                                                                    <Column field="finding" header="Finding"></Column>
                                                                    <Column field="description" header="Description"></Column> */}
                                                                {/* Example of showing how many files */}
                                                                {/* <Column
                                                                    header="Files"
                                                                    body={(rowData) => rowData.auditorAttachments && rowData.auditorAttachments.length}
                                                                ></Column> */}
                                                                {/* {auditId?.auditorAssignmentSubmission?.type ? null : <Column body={actionBodyTemplate} header="Actions" style={{ minWidth: '8rem' }}></Column>}
                                                                </DataTable> */}
                                                            </>


                                                        }
                                                    </AccordionTab>
                                                )
                                            })
                                            }
                                        </Accordion>
                                    </Tab.Pane>)
                                })}
                            </Tab.Content>



                        </Col>


                    </Row>
                </Tab.Container>
                :
                <div className="col-12 flex justify-content-center"><i className="fs-36 pi pi-spin pi-spinner" /> </div>

            }

            <div className="mt-4" style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px' }}>
                {/* Close button */}
                <Button
                    label={isClosing ? "Saving & Closing..." : "Close"}
                    icon={isClosing ? "pi pi-spin pi-spinner" : ""}
                    className="p-button-secondary"
                    onClick={handleCloseWithAutoSave}
                    disabled={isClosing}
                />

                {/* Outlined + warning color */}
                {/* {((assessmentsection?.length && selectedsubsection2 && selectSection && selectSection.form?.data1 && !auditId?.auditorAssignmentSubmission?.type)) && editable && <Button
                    label="Save Section Progress"
                    className=" p-button-warning"
                    onClick={() => handleProgress()}
                />} */}

                {/* <Button
                    label={isAutoSaving ? "Auto Saving..." : "Save Section Progress"}
                    icon={isAutoSaving ? "pi pi-spin pi-spinner" : "pi pi-save"}
                    className=" p-button-warning"
                    onClick={() => handleProgress()}
                    disabled={isAutoSaving}
                /> */}

                {/* Outlined + success color */}
                {/* {validateAssessment(assessmentsection).valid && !auditId?.auditorAssignmentSubmission?.type && editable &&
                    <Button
                        label="Submit"
                        onClick={handleSubmit}
                        disabled={!validateAssessment(assessmentsection).valid}  // Only enabled if everything is completed
                    />} */}

                <Button
                    label="Submit"
                    onClick={handleSubmit}
                    disabled={
                        !allSectionsComplete ||              // new rule → at least one circle still grey/orange
                        !validateAssessment(assessmentsection).valid ||
                        auditId?.auditorAssignmentSubmission?.type || // keep your existing guards
                        !editable
                    }
                />


            </div>




            <Dialog
                header={
                    'Add Comment '
                }
                visible={showModal}
                style={{ width: '75%' }}
                onHide={handleCloseModal}
            >
                <div>
                    <InputTextarea
                        value={comment}
                        disabled={!editable}
                        onChange={(e) => setComment(e.target.value)}
                        style={{ width: '100%', height: 150, overflow: 'auto' }}
                    />
                    <div className="flex justify-content-end col-12">
                        <Button
                            style={{ width: 110 }}
                            className="mr-2"
                            label="Close"
                            onClick={() => {
                                setShowModal(false);
                            }}
                        />
                        {editable && <Button
                            style={{ width: 110 }}
                            label="Save & Exit"
                            onClick={() => handleSaveComment()}
                        />}
                    </div>
                </div>
            </Dialog>

            <Dialog
                visible={findingDialog}
                style={{ width: '65%' }}
                header={`Finding Details of "${selectSubSection1.title}"`}
                modal
                className="p-fluid"
                footer={findingDialogFooter}
                onHide={hideDialog}
            >
                <div className="field">
                    <label htmlFor="categoryOfFinding">Category of Finding</label>
                    <Dropdown
                        id="categoryOfFinding"
                        optionLabel="label"
                        optionValue="id"
                        value={currentFinding.categoryOfFinding}
                        options={categoryOptions}
                        onChange={(e) => setCurrentFinding({ ...currentFinding, categoryOfFinding: e.value })}
                        placeholder="Select Category"
                    />
                </div>

                {currentFinding.categoryOfFinding === 3 && (
                    <>
                        <div className="field">
                            <label htmlFor="nonComplianceType">Non-Compliance Type</label>
                            <Dropdown
                                id="nonComplianceType"
                                optionLabel="label"
                                optionValue="id"
                                value={currentFinding.nonComplianceType}
                                options={nonComplianceOptions}
                                onChange={(e) =>
                                    setCurrentFinding({ ...currentFinding, nonComplianceType: e.value })
                                }
                                placeholder="Select Type"
                            />
                        </div>
                        <div className="field">
                            <label htmlFor="applicableLaw">Applicable Law</label>
                            <Dropdown
                                id="applicableLaw"
                                filter
                                panelClassName="hidefilter"
                                value={currentFinding.applicableLaw}
                                options={lawsOption}
                                optionLabel="title"
                                optionValue="id"
                                onChange={(e) => setCurrentFinding({ ...currentFinding, applicableLaw: e.value })}
                                placeholder="Select Law"
                            />
                        </div>
                        {currentFinding?.applicableLaw === 0 && <div className="field">
                            <label htmlFor="otherlaw">Please mention name of Law<span className="ml-1 mandatory">*</span> </label>
                            <InputText
                                id="otherlaw"
                                value={currentFinding.otherLaw}
                                onChange={(e) => setCurrentFinding({ ...currentFinding, otherLaw: e.target.value })}
                            />
                        </div>}
                    </>
                )}



                <div className="field">
                    <label htmlFor="finding">Provide a short title for this observation</label>
                    <InputText
                        id="finding"
                        value={currentFinding.finding}
                        onChange={(e) => setCurrentFinding({ ...currentFinding, finding: e.target.value })}
                    />
                </div>

                <div className="field">
                    <label htmlFor="description">Describe the observation in detail. Where applicable, refer to the samples and supporting evidence for the finding.</label>
                    <InputTextarea
                        id="description"
                        rows={2}
                        value={currentFinding.description}
                        onChange={(e) => setCurrentFinding({ ...currentFinding, description: e.target.value })}
                    />
                </div>



                <div
                    style={{
                        marginBottom: "15px",
                        padding: "15px",
                        border: "1px solid #ddd",
                        borderRadius: "5px",
                        backgroundColor: "#f9f9f9",
                    }}
                >
                    <h1 style={{ marginBottom: "15px", fontSize: 15, color: "#333" }}>Upload Evidence</h1>

                    <input
                        type="file"
                        multiple
                        accept=".png, .jpg, .jpeg, .pdf, .doc, .docx"
                        onChange={(e) => handleFileUpload(e.target.files)}
                        style={{
                            display: "block",
                            marginBottom: "15px",
                            padding: "10px",
                            border: "1px solid #ccc",
                            borderRadius: "4px",
                            width: "100%",
                            backgroundColor: "#fff",
                            color: "#333",
                        }}
                    />

                    {/* Display Uploaded Files */}
                    {currentFinding.auditorAttachments?.length > 0 && (
                        <div style={{ marginTop: "10px" }}>
                            <h1 style={{ color: "#555", fontSize: 15, marginBottom: "10px" }}>Uploaded Files:</h1>
                            <ul style={{ listStyleType: "none", padding: 0 }}>
                                {currentFinding.auditorAttachments?.map((file, fileIndex) => (
                                    <li
                                        key={fileIndex}
                                        style={{
                                            display: "flex",
                                            justifyContent: "space-between",
                                            alignItems: "center",
                                            marginBottom: "8px",
                                            padding: "10px",
                                            backgroundColor: "#fff",
                                            border: "1px solid #ccc",
                                            borderRadius: "5px",
                                        }}
                                    >
                                        <span onClick={() => { window.open(API.Docs + file.originalname) }} style={{ overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap" }}>
                                            {file.originalname}
                                        </span>
                                        <Button
                                            icon="pi pi-trash"
                                            className="p-button-danger p-button-sm"
                                            onClick={() => handleFileRemove(fileIndex)}
                                        />
                                    </li>
                                ))}
                            </ul>
                        </div>
                    )}
                </div>


            </Dialog>

            {/* DELETE CONFIRMATION DIALOG */}
            <Dialog
                visible={deleteDialog}
                style={{ width: '350px' }}
                header="Confirm"
                modal
                footer={deleteDialogFooter}
                onHide={hideDeleteDialog}
            >
                <div className="confirmation-content">
                    <i
                        className="pi pi-exclamation-triangle mr-3"
                        style={{ fontSize: '2rem' }}
                    />
                    {currentFinding && (
                        <span>
                            Are you sure you want to delete <b>{currentFinding.finding}</b>?
                        </span>
                    )}
                </div>
            </Dialog>
            <Dialog visible={reportdialog} style={{ width: '75%' }} onHide={() => {
                setReportDialog(false);
                // Trigger auto save when report dialog is closed (only if editable)
                if (editable) {
                    console.log("Report dialog closed - triggering auto save");
                    performAutoSaveRef.current();
                }
            }} >
                <SupplierReport report={auditId} disablePrint={true} />
            </Dialog>
        </>
    );
};

const Stepper = ({ steps, onClickStep, labelColor = 'black', activeBGColor = '#6c757d', activeIndex = 0, isLoading = false }) => {

    return (
        <div style={{ position: 'relative', display: 'flex', justifyContent: 'center' }}>
            {/* Loading overlay */}
            {isLoading && (
                <div style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: 10,
                    borderRadius: '8px'
                }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                        <i className="pi pi-spin pi-spinner" style={{ fontSize: '20px', color: '#007bff' }} />
                        <span style={{ fontSize: '14px', fontWeight: '600', color: '#007bff' }}>
                            Saving & Loading Section...
                        </span>
                    </div>
                </div>
            )}

            {steps.map((step, index) => (
                <React.Fragment key={index}>
                    <div style={{ position: 'relative' }}>
                        <div
                            onClick={() => !isLoading && onClickStep && onClickStep(step, index)}
                            style={{
                                display: 'flex',
                                position: 'relative',
                                flexDirection: 'column',
                                zIndex: 1,
                                alignItems: 'center',
                                cursor: isLoading ? 'not-allowed' : (activeIndex !== index ? 'pointer' : 'default'),
                                textAlign: 'center',
                                width: '170px', // Fixed width to keep all steps aligned
                                opacity: isLoading ? 0.6 : 1,
                            }}
                        >
                            <div
                                style={{
                                    backgroundColor: step.completed === true
                                        ? 'green'                    // All questions answered
                                        : step.hasProgress === true
                                            ? 'rgb(255, 165, 0)'    // Some questions answered (yellow/orange)
                                            : 'grey',               // No questions answered
                                    color: '#fff',
                                    borderRadius: '50%',
                                    width: '40px',
                                    height: '40px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    fontSize: '18px',
                                    fontWeight: 700,
                                    transition: 'background-color 0.3s ease', // Smooth color transition
                                }}
                            >
                                {index + 1} {/* Display step number starting from 1 */}
                            </div>
                            <div
                                style={{
                                    fontSize: 16,
                                    marginTop: '8px',
                                    fontWeight: activeIndex === index ? 700 : 'normal', // Adjusted for 1-based index
                                    color: activeIndex === index ? labelColor : 'black',
                                    textDecoration: activeIndex === index ? 'underline' : 'none',
                                }}
                            >
                                {step.label}
                            </div>
                        </div>

                        {/* Add line between steps, except for the last step */}
                        {index < steps.length - 1 && (
                            <hr
                                style={{
                                    alignItems: 'center',
                                    display: 'flex',
                                    justifyContent: 'center',
                                    left: '50%',
                                    top: '8px',
                                    width: '100%',
                                    position: 'absolute',
                                    zIndex: 0,
                                }}
                            />
                        )}
                    </div>
                </React.Fragment>
            ))}
        </div>
    );
};

export default AuditPanel;